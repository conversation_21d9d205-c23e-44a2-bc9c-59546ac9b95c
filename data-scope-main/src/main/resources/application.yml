spring:
  application:
    name: data-scope

  # Database Configuration
  datasource:
    dynamic:
      strict: false
      primary: master
      datasource:
        master:
          type: com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSource
          poolName: DATA_SCOPE

  # Web配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    async:
      request-timeout: 30000
    servlet:
      load-on-startup: 1
      multipart:
        enabled: true
        max-file-size: 10MB
        max-request-size: 10MB
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 3600
    servlet:
      encoding:
        charset: UTF-8
        force: true
        enabled: true

  # Redis配置
#  data:
#    redis:
#      timeout: 2000
#      sentinel:
#        master: mymaster
#        nodes: redis.bass.3g:26379
#      password: XjVnumoTigyY8oIxgWllMw==
#      client-type: lettuce
#      lettuce:
#        pool:
#          max-active: 8
#          max-wait: 5000
#          max-idle: 8
#          min-idle: 0

  # 缓存配置
  cache:
    # 缓存类型: redis或simple
    # 如果Redis服务器不可用，可以将此值改为simple
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: false
    # 预定义的缓存名称
    cache-names: metadata,query-result

server:
  port: 8080
  servlet:
    context-path: /data-scope
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
    min-response-size: 1024

# 日志配置
logging:
  level:
    root: INFO
    com.datascope: INFO
    org.apache: INFO
    org.springframework: WARN
  file:
    name: logs/insight-data.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    # Hibernate logging removed as we are not using JPA/Hibernate

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# SpringDoc OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  packages-to-scan: com.datascope

# 应用自定义配置
datascope:
  # 数据源配置
  datasource:
    max-connections: ${MAX_CONNECTIONS:50}
    query-timeout-seconds: ${QUERY_TIMEOUT:30}
    max-download-records: ${MAX_DOWNLOAD:50000}

    # 连接池配置
    connection-pool:
      # 基本配置
      default-max-size: 50
      default-min-idle: 10
      default-idle-timeout: 600000
      default-max-lifetime: 1800000
      default-connection-timeout: 30000

      # 监控配置
      metrics:
        enabled: true
        reporter: micrometer
        tags:
          application: ${spring.application.name}

      # 健康检查配置
      health-check:
        enabled: true
        interval: 60000
        timeout: 5000
        failure-threshold: 3

      # 告警配置
      alerts:
        enabled: true
        pool-usage-threshold: 80
        connection-timeout-threshold: 1000
        leak-detection-threshold: 60000

    # 元数据同步配置
    metadata-sync:
      default-timeout: 3600 # 默认同步超时时间（秒）
      max-concurrent-jobs: 3 # 最大并发同步任务数
      extraction-threads: 10 # 元数据提取线程数

  # 查询配置
  query:
    default-timeout: 30 # 默认查询超时时间（秒）
    max-timeout: 300 # 最大查询超时时间（秒）
    default-max-rows: 1000 # 默认最大返回行数
    max-rows-limit: 50000 # 最大行数限制
    max-concurrent-queries: 20 # 最大并发查询数

  # 缓存配置
  cache:
    metadata:
      expiration: 86400 # 元数据缓存过期时间（秒）
    query-result:
      expiration: 600 # 查询结果缓存过期时间（秒）

  # 安全配置
  security:
    aes-key: ${AES_KEY:default-aes-key-do-not-use-in-production}
    salt: ${SALT:default-salt-do-not-use-in-production}
    jwt:
      secret: your-secret-key-here
      expiration: 86400 # Token过期时间（秒）
    encryption:
      algorithm: AES
      key-size: 256
      key-rotation-days: 90
  openrouter:
    api-key: ${OPENROUTER_API_KEY}
    model: ${OPENROUTER_MODEL:mistralai/mistral-7b-instruct}
    max-tokens: ${MAX_TOKENS:1000}
  rate-limit:
    max-requests-per-second: ${MAX_REQUESTS:10}
  llm:
    api:
      url: https://router.requesty.ai/v1
      key: sk-BvAEEuz3TkCzWxfZPKLJCl7FXj7dNK9ZkC1dkfu9Ghqh2mF7athO/QgqRLD3UxPyHaJTVec8/lHGiQIOPaiGzJhXGwbQZyWmxLeY74L4fQQ=

# Security Configuration
security:
  encryption:
    master-key: ${ENCRYPTION_MASTER_KEY:default-master-key-do-not-use-in-production}
    key-rotation-interval: 30d

project:
  filter: false

#uiaservice:
#  url: http://yuia.service.boss.yp/yuia-service-boss
#  inner:
#    url: http://yuia.service.boss.yp:30422/yuia-service-boss
#callback:
#  url: https://boss.yeepay.com/data-scope/api/login

# OpenRouter LLM配置
openrouter:
  api-key: ${OPENROUTER_API_KEY: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}
  model: ${OPENROUTER_MODEL:deepseek/deepseek-chat-v3-0324:free}
  max-tokens: ${OPENROUTER_MAX_TOKENS:4000}
  api-url: ${OPENROUTER_API_URL:https://openrouter.ai/api/v1/chat/completions}
