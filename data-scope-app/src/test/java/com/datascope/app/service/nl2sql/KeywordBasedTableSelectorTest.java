package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.impl.KeywordBasedTableSelector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 关键词表选择器测试
 */
class KeywordBasedTableSelectorTest {

    private KeywordBasedTableSelector tableSelector;
    private List<TableDTO> testTables;
    private Map<String, List<ColumnDTO>> testColumns;

    @BeforeEach
    void setUp() {
        tableSelector = new KeywordBasedTableSelector();
        setupTestData();
    }

    private void setupTestData() {
        testTables = new ArrayList<>();
        testColumns = new HashMap<>();

        // 创建测试表1: 学生表
        TableDTO studentTable = TableDTO.builder()
                .id("table1")
                .name("student_info")
                .description("学生基本信息表")
                .build();
        testTables.add(studentTable);

        List<ColumnDTO> studentColumns = Arrays.asList(
                ColumnDTO.builder().name("id").dataType("bigint").description("学生ID").build(),
                ColumnDTO.builder().name("name").dataType("varchar").description("学生姓名").build(),
                ColumnDTO.builder().name("age").dataType("int").description("年龄").build(),
                ColumnDTO.builder().name("class_id").dataType("bigint").description("班级ID").build()
        );
        testColumns.put("table1", studentColumns);

        // 创建测试表2: 成绩表
        TableDTO scoreTable = TableDTO.builder()
                .id("table2")
                .name("student_score")
                .description("学生成绩表")
                .build();
        testTables.add(scoreTable);

        List<ColumnDTO> scoreColumns = Arrays.asList(
                ColumnDTO.builder().name("id").dataType("bigint").description("成绩ID").build(),
                ColumnDTO.builder().name("student_id").dataType("bigint").description("学生ID").build(),
                ColumnDTO.builder().name("subject").dataType("varchar").description("科目").build(),
                ColumnDTO.builder().name("score").dataType("decimal").description("分数").build()
        );
        testColumns.put("table2", scoreColumns);

        // 创建测试表3: 订单表
        TableDTO orderTable = TableDTO.builder()
                .id("table3")
                .name("order_info")
                .description("订单信息表")
                .build();
        testTables.add(orderTable);

        List<ColumnDTO> orderColumns = Arrays.asList(
                ColumnDTO.builder().name("id").dataType("bigint").description("订单ID").build(),
                ColumnDTO.builder().name("user_id").dataType("bigint").description("用户ID").build(),
                ColumnDTO.builder().name("amount").dataType("decimal").description("金额").build(),
                ColumnDTO.builder().name("create_time").dataType("datetime").description("创建时间").build()
        );
        testColumns.put("table3", orderColumns);

        // 创建测试表4: 用户表
        TableDTO userTable = TableDTO.builder()
                .id("table4")
                .name("user_info")
                .description("用户信息表")
                .build();
        testTables.add(userTable);

        List<ColumnDTO> userColumns = Arrays.asList(
                ColumnDTO.builder().name("id").dataType("bigint").description("用户ID").build(),
                ColumnDTO.builder().name("username").dataType("varchar").description("用户名").build(),
                ColumnDTO.builder().name("email").dataType("varchar").description("邮箱").build()
        );
        testColumns.put("table4", userColumns);
    }

    @Test
    void testSelectStudentScoreTables() {
        String question = "查询学生成绩靠前的top3";
        
        TableSelector.TableSelectionResult result = tableSelector.selectRelevantTables(question, testTables, testColumns);
        
        assertNotNull(result);
        assertFalse(result.getSelectedTables().isEmpty());
        assertTrue(result.getConfidence() > 0.5);
        
        // 应该选择学生表和成绩表
        List<String> selectedTableNames = result.getSelectedTables().stream()
                .map(TableDTO::getName)
                .collect(Collectors.toList());
        
        assertTrue(selectedTableNames.contains("student_info") || selectedTableNames.contains("student_score"));
        
        System.out.println("问题: " + question);
        System.out.println("选择的表: " + selectedTableNames);
        System.out.println("置信度: " + result.getConfidence());
        System.out.println("原因: " + result.getReason());
    }

    @Test
    void testSelectOrderTables() {
        String question = "统计用户订单数量";
        
        TableSelector.TableSelectionResult result = tableSelector.selectRelevantTables(question, testTables, testColumns);
        
        assertNotNull(result);
        assertFalse(result.getSelectedTables().isEmpty());
        
        List<String> selectedTableNames = result.getSelectedTables().stream()
                .map(TableDTO::getName)
                .collect(Collectors.toList());
        
        // 应该选择用户表和订单表
        assertTrue(selectedTableNames.contains("user_info") || selectedTableNames.contains("order_info"));
        
        System.out.println("问题: " + question);
        System.out.println("选择的表: " + selectedTableNames);
        System.out.println("置信度: " + result.getConfidence());
        System.out.println("原因: " + result.getReason());
    }

    @Test
    void testNoRelevantTables() {
        String question = "今天天气怎么样";
        
        TableSelector.TableSelectionResult result = tableSelector.selectRelevantTables(question, testTables, testColumns);
        
        assertNotNull(result);
        // 可能没有选择任何表，或者置信度很低
        assertTrue(result.getConfidence() < 0.3);
        
        System.out.println("问题: " + question);
        System.out.println("选择的表: " + result.getSelectedTables().stream().map(TableDTO::getName).collect(Collectors.toList()));
        System.out.println("置信度: " + result.getConfidence());
        System.out.println("原因: " + result.getReason());
    }
}
