package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.impl.KeywordBasedTableSelector;

import java.util.*;

/**
 * 支付场景表选择器演示程序
 * 展示在支付公司场景下的智能表选择效果
 */
public class PaymentTableSelectorDemo {

    public static void main(String[] args) {
        KeywordBasedTableSelector tableSelector = new KeywordBasedTableSelector();
        
        // 创建支付场景测试数据
        List<TableDTO> testTables = createPaymentTables();
        Map<String, List<ColumnDTO>> testColumns = createPaymentColumns();
        
        System.out.println("=== 支付公司智能表选择器演示 ===\n");
        System.out.println("支付系统共有 " + testTables.size() + " 个表：");
        for (TableDTO table : testTables) {
            System.out.println("- " + table.getName() + " (" + table.getDescription() + ")");
        }
        System.out.println();
        
        // 支付公司典型查询场景
        String[] questions = {
            "查询今天的支付交易流水",
            "统计各渠道的成功率",
            "查找失败的退款订单",
            "查询商户的结算金额",
            "统计银行代付的手续费",
            "查询账户余额变动记录",
            "分析渠道路由配置",
            "查询对账差异数据",
            "统计充值提现比例",
            "查询冻结账户信息"
        };
        
        for (String question : questions) {
            System.out.println("问题: " + question);
            System.out.println("---");
            
            TableSelector.TableSelectionResult result = tableSelector.selectRelevantTables(question, testTables, testColumns);
            
            List<String> selectedTableNames = new ArrayList<>();
            for (TableDTO table : result.getSelectedTables()) {
                selectedTableNames.add(table.getName());
            }
            
            System.out.println("选择的表: " + selectedTableNames);
            System.out.printf("置信度: %.1f%%\n", result.getConfidence() * 100);
            System.out.println("Token节省: " + calculateTokenSaving(testTables.size(), result.getSelectedTables().size()));
            System.out.println("选择原因:");
            System.out.println(result.getReason());
            System.out.println();
        }
    }
    
    private static List<TableDTO> createPaymentTables() {
        List<TableDTO> tables = new ArrayList<>();
        
        // 核心支付表
        tables.add(TableDTO.builder().id("1").name("payment_order").description("支付订单表").build());
        tables.add(TableDTO.builder().id("2").name("payment_transaction").description("支付交易流水表").build());
        tables.add(TableDTO.builder().id("3").name("refund_order").description("退款订单表").build());
        tables.add(TableDTO.builder().id("4").name("settlement_record").description("结算记录表").build());
        
        // 商户和渠道
        tables.add(TableDTO.builder().id("5").name("merchant_info").description("商户信息表").build());
        tables.add(TableDTO.builder().id("6").name("channel_info").description("支付渠道表").build());
        tables.add(TableDTO.builder().id("7").name("channel_route").description("渠道路由配置表").build());
        tables.add(TableDTO.builder().id("8").name("channel_rate").description("渠道费率表").build());
        
        // 账户相关
        tables.add(TableDTO.builder().id("9").name("account_info").description("账户信息表").build());
        tables.add(TableDTO.builder().id("10").name("account_balance").description("账户余额表").build());
        tables.add(TableDTO.builder().id("11").name("account_freeze").description("账户冻结表").build());
        tables.add(TableDTO.builder().id("12").name("account_flow").description("账户流水表").build());
        
        // 对账和异常
        tables.add(TableDTO.builder().id("13").name("reconcile_record").description("对账记录表").build());
        tables.add(TableDTO.builder().id("14").name("reconcile_exception").description("对账差异表").build());
        tables.add(TableDTO.builder().id("15").name("payment_exception").description("支付异常表").build());
        
        // 银行和第三方
        tables.add(TableDTO.builder().id("16").name("bank_info").description("银行信息表").build());
        tables.add(TableDTO.builder().id("17").name("bank_transaction").description("银行交易表").build());
        tables.add(TableDTO.builder().id("18").name("third_party_notify").description("第三方通知表").build());
        
        // 充值提现
        tables.add(TableDTO.builder().id("19").name("recharge_order").description("充值订单表").build());
        tables.add(TableDTO.builder().id("20").name("withdraw_order").description("提现订单表").build());
        
        return tables;
    }
    
    private static Map<String, List<ColumnDTO>> createPaymentColumns() {
        Map<String, List<ColumnDTO>> columns = new HashMap<>();
        
        // payment_order
        columns.put("1", Arrays.asList(
            ColumnDTO.builder().name("order_id").dataType("varchar").description("订单号").build(),
            ColumnDTO.builder().name("merchant_id").dataType("varchar").description("商户号").build(),
            ColumnDTO.builder().name("amount").dataType("decimal").description("支付金额").build(),
            ColumnDTO.builder().name("status").dataType("varchar").description("订单状态").build(),
            ColumnDTO.builder().name("channel_id").dataType("varchar").description("渠道ID").build()
        ));
        
        // payment_transaction
        columns.put("2", Arrays.asList(
            ColumnDTO.builder().name("txn_id").dataType("varchar").description("交易流水号").build(),
            ColumnDTO.builder().name("order_id").dataType("varchar").description("订单号").build(),
            ColumnDTO.builder().name("txn_type").dataType("varchar").description("交易类型").build(),
            ColumnDTO.builder().name("txn_status").dataType("varchar").description("交易状态").build(),
            ColumnDTO.builder().name("success_time").dataType("datetime").description("成功时间").build()
        ));
        
        // channel_info
        columns.put("6", Arrays.asList(
            ColumnDTO.builder().name("channel_id").dataType("varchar").description("渠道ID").build(),
            ColumnDTO.builder().name("channel_name").dataType("varchar").description("渠道名称").build(),
            ColumnDTO.builder().name("success_rate").dataType("decimal").description("成功率").build(),
            ColumnDTO.builder().name("channel_type").dataType("varchar").description("渠道类型").build()
        ));
        
        // account_balance
        columns.put("10", Arrays.asList(
            ColumnDTO.builder().name("account_id").dataType("varchar").description("账户ID").build(),
            ColumnDTO.builder().name("balance").dataType("decimal").description("可用余额").build(),
            ColumnDTO.builder().name("frozen_amount").dataType("decimal").description("冻结金额").build(),
            ColumnDTO.builder().name("update_time").dataType("datetime").description("更新时间").build()
        ));
        
        // 为其他表添加基本字段
        for (String tableId : Arrays.asList("3", "4", "5", "7", "8", "9", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20")) {
            columns.put(tableId, Arrays.asList(
                ColumnDTO.builder().name("id").dataType("varchar").description("主键ID").build(),
                ColumnDTO.builder().name("create_time").dataType("datetime").description("创建时间").build(),
                ColumnDTO.builder().name("status").dataType("varchar").description("状态").build()
            ));
        }
        
        return columns;
    }
    
    private static String calculateTokenSaving(int totalTables, int selectedTables) {
        if (totalTables == 0) return "0%";
        double savingRate = (double)(totalTables - selectedTables) / totalTables * 100;
        return String.format("%.0f%% (从%d个表减少到%d个表)", savingRate, totalTables, selectedTables);
    }
}
