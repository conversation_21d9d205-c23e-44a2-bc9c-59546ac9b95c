package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.impl.KeywordBasedTableSelector;

import java.util.*;

/**
 * 表选择器演示程序
 * 展示智能表选择器的效果
 */
public class TableSelectorDemo {

    public static void main(String[] args) {
        KeywordBasedTableSelector tableSelector = new KeywordBasedTableSelector();
        
        // 创建测试数据
        List<TableDTO> testTables = createTestTables();
        Map<String, List<ColumnDTO>> testColumns = createTestColumns();
        
        System.out.println("=== 智能表选择器演示 ===\n");
        System.out.println("数据库共有 " + testTables.size() + " 个表：");
        for (TableDTO table : testTables) {
            System.out.println("- " + table.getName() + " (" + table.getDescription() + ")");
        }
        System.out.println();
        
        // 测试不同的问题
        String[] questions = {
            "查询学生成绩靠前的top3",
            "统计每个用户的订单数量", 
            "查找上个月销售额最高的产品",
            "查询员工所在的部门信息",
            "今天天气怎么样"
        };
        
        for (String question : questions) {
            System.out.println("问题: " + question);
            System.out.println("---");
            
            TableSelector.TableSelectionResult result = tableSelector.selectRelevantTables(question, testTables, testColumns);
            
            List<String> selectedTableNames = new ArrayList<>();
            for (TableDTO table : result.getSelectedTables()) {
                selectedTableNames.add(table.getName());
            }
            
            System.out.println("选择的表: " + selectedTableNames);
            System.out.printf("置信度: %.1f%%\n", result.getConfidence() * 100);
            System.out.println("Token节省: " + calculateTokenSaving(testTables.size(), result.getSelectedTables().size()));
            System.out.println("选择原因:");
            System.out.println(result.getReason());
            System.out.println();
        }
    }
    
    private static List<TableDTO> createTestTables() {
        List<TableDTO> tables = new ArrayList<>();
        
        // 学生相关表
        tables.add(TableDTO.builder().id("1").name("student_info").description("学生基本信息表").build());
        tables.add(TableDTO.builder().id("2").name("student_score").description("学生成绩表").build());
        tables.add(TableDTO.builder().id("3").name("course_info").description("课程信息表").build());
        tables.add(TableDTO.builder().id("4").name("class_info").description("班级信息表").build());
        
        // 用户订单相关表
        tables.add(TableDTO.builder().id("5").name("user_info").description("用户信息表").build());
        tables.add(TableDTO.builder().id("6").name("order_info").description("订单信息表").build());
        tables.add(TableDTO.builder().id("7").name("order_item").description("订单明细表").build());
        tables.add(TableDTO.builder().id("8").name("product_info").description("商品信息表").build());
        
        // 员工部门相关表
        tables.add(TableDTO.builder().id("9").name("employee_info").description("员工信息表").build());
        tables.add(TableDTO.builder().id("10").name("department_info").description("部门信息表").build());
        
        // 其他表
        tables.add(TableDTO.builder().id("11").name("system_log").description("系统日志表").build());
        tables.add(TableDTO.builder().id("12").name("config_info").description("配置信息表").build());
        
        return tables;
    }
    
    private static Map<String, List<ColumnDTO>> createTestColumns() {
        Map<String, List<ColumnDTO>> columns = new HashMap<>();
        
        // student_info
        columns.put("1", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("学生ID").build(),
            ColumnDTO.builder().name("name").dataType("varchar").description("学生姓名").build(),
            ColumnDTO.builder().name("age").dataType("int").description("年龄").build(),
            ColumnDTO.builder().name("class_id").dataType("bigint").description("班级ID").build()
        ));
        
        // student_score
        columns.put("2", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("成绩ID").build(),
            ColumnDTO.builder().name("student_id").dataType("bigint").description("学生ID").build(),
            ColumnDTO.builder().name("course_id").dataType("bigint").description("课程ID").build(),
            ColumnDTO.builder().name("score").dataType("decimal").description("分数").build()
        ));
        
        // user_info
        columns.put("5", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("用户ID").build(),
            ColumnDTO.builder().name("username").dataType("varchar").description("用户名").build(),
            ColumnDTO.builder().name("email").dataType("varchar").description("邮箱").build()
        ));
        
        // order_info
        columns.put("6", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("订单ID").build(),
            ColumnDTO.builder().name("user_id").dataType("bigint").description("用户ID").build(),
            ColumnDTO.builder().name("total_amount").dataType("decimal").description("总金额").build(),
            ColumnDTO.builder().name("create_time").dataType("datetime").description("创建时间").build()
        ));
        
        // product_info
        columns.put("8", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("商品ID").build(),
            ColumnDTO.builder().name("name").dataType("varchar").description("商品名称").build(),
            ColumnDTO.builder().name("price").dataType("decimal").description("价格").build(),
            ColumnDTO.builder().name("sales_amount").dataType("decimal").description("销售额").build()
        ));
        
        // employee_info
        columns.put("9", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("员工ID").build(),
            ColumnDTO.builder().name("name").dataType("varchar").description("员工姓名").build(),
            ColumnDTO.builder().name("department_id").dataType("bigint").description("部门ID").build()
        ));
        
        // department_info
        columns.put("10", Arrays.asList(
            ColumnDTO.builder().name("id").dataType("bigint").description("部门ID").build(),
            ColumnDTO.builder().name("name").dataType("varchar").description("部门名称").build(),
            ColumnDTO.builder().name("manager_id").dataType("bigint").description("部门经理ID").build()
        ));
        
        // 为其他表添加基本字段
        for (String tableId : Arrays.asList("3", "4", "7", "11", "12")) {
            columns.put(tableId, Arrays.asList(
                ColumnDTO.builder().name("id").dataType("bigint").description("主键ID").build(),
                ColumnDTO.builder().name("name").dataType("varchar").description("名称").build()
            ));
        }
        
        return columns;
    }
    
    private static String calculateTokenSaving(int totalTables, int selectedTables) {
        if (totalTables == 0) return "0%";
        double savingRate = (double)(totalTables - selectedTables) / totalTables * 100;
        return String.format("%.0f%% (从%d个表减少到%d个表)", savingRate, totalTables, selectedTables);
    }
}
