package com.datascope.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * LLM配置类
 * 用于配置OpenRouter和LLM API的相关属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "openrouter")
public class LLMConfig {

    /**
     * OpenRouter API密钥
     */
    private String apiKey;

    /**
     * 使用的模型名称
     */
    private String model;

    /**
     * 最大token数
     */
    private Integer maxTokens;

    /**
     * API URL
     */
    private String apiUrl = "https://openrouter.ai/api/v1/chat/completions";
}
