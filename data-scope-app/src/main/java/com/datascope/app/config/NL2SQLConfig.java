package com.datascope.app.config;

import com.datascope.app.service.nl2sql.TableSelector;
import com.datascope.app.service.nl2sql.impl.KeywordBasedTableSelector;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自然语言转SQL相关配置
 */
@Configuration
public class NL2SQLConfig {

    /**
     * 表选择器Bean配置
     * 默认使用基于关键词的实现，可以通过自定义Bean来替换
     */
    @Bean
    @ConditionalOnMissingBean(TableSelector.class)
    public TableSelector tableSelector() {
        return new KeywordBasedTableSelector();
    }
}
