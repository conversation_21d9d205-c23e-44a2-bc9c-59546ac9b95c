package com.datascope.app.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 跨库查询配置
 */
@Configuration
@ConfigurationProperties(prefix = "datascope.cross-database")
public class CrossDatabaseConfig {

    /**
     * 是否启用跨库查询
     */
    private boolean enabled = true;

    /**
     * 数据库别名映射
     */
    private Map<String, String> databaseAliases = new HashMap<>();

    /**
     * 跨库表关联关系
     */
    private Map<String, String[]> tableRelationships = new HashMap<>();

    public CrossDatabaseConfig() {
        // 初始化默认的数据库别名
        databaseAliases.put("payment", "payment_db");
        databaseAliases.put("order", "order_db");
        databaseAliases.put("user", "user_db");
        databaseAliases.put("inventory", "inventory_db");

        // 初始化默认的表关联关系
        tableRelationships.put("user_info", new String[]{"payment_db.user_account", "order_db.user_profile"});
        tableRelationships.put("order_info", new String[]{"payment_db.transaction", "inventory_db.product"});
        tableRelationships.put("product_info", new String[]{"order_db.order_item", "inventory_db.stock"});
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Map<String, String> getDatabaseAliases() {
        return databaseAliases;
    }

    public void setDatabaseAliases(Map<String, String> databaseAliases) {
        this.databaseAliases = databaseAliases;
    }

    public Map<String, String[]> getTableRelationships() {
        return tableRelationships;
    }

    public void setTableRelationships(Map<String, String[]> tableRelationships) {
        this.tableRelationships = tableRelationships;
    }
} 