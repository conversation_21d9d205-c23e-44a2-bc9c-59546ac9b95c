package com.datascope.app.service.llm.impl;

import com.datascope.app.config.LLMConfig;
import com.datascope.app.service.llm.LLMIntegrationService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * LLM集成服务实现类
 * 负责与OpenRouter LLM服务集成，处理API调用和响应
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LLMIntegrationServiceImpl implements LLMIntegrationService {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper;
    private final LLMConfig llmConfig;

    @Override
    public String sendPrompt(String prompt) {
        return sendPrompt(prompt, new HashMap<>());
    }

    @Override
    public String sendPrompt(String prompt, Map<String, Object> parameters) {
        log.info("发送提示到LLM: {}", prompt);
        try {
            // 准备请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + llmConfig.getApiKey());

            // 准备请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", llmConfig.getModel());

            // 创建消息对象
            Map<String, String> messageMap = new HashMap<>();
            messageMap.put("role", "user");
            messageMap.put("content", prompt);

            requestBody.put("messages", new Object[]{ messageMap });
            requestBody.put("max_tokens", parameters.getOrDefault("max_tokens", llmConfig.getMaxTokens()));

            // 添加其他可选参数
            if (parameters.containsKey("temperature")) {
                requestBody.put("temperature", parameters.get("temperature"));
            }
            if (parameters.containsKey("top_p")) {
                requestBody.put("top_p", parameters.get("top_p"));
            }

            // 发送请求
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(llmConfig.getApiUrl(), request, String.class);

            // 解析响应
            String responseBody = response.getBody();
            log.info("LLM响应: {}", responseBody);

            JsonNode jsonNode = objectMapper.readTree(responseBody);
            String content = jsonNode.path("choices").path(0).path("message").path("content").asText();

            log.info("LLM响应内容: {}", content);
            return content;

        } catch (Exception e) {
            log.error("调用LLM API失败", e);
            throw new RuntimeException("调用LLM API失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getModelName() {
        return llmConfig.getModel();
    }

    @Override
    public int getMaxTokens() {
        return llmConfig.getMaxTokens();
    }
}
