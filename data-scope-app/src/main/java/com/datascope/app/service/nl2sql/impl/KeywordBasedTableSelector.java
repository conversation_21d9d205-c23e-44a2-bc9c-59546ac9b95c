package com.datascope.app.service.nl2sql.impl;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.TableSelector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于关键词匹配的表选择器实现
 * 通过分析自然语言中的关键词来匹配相关的表和字段
 */
@Slf4j
@Service
public class KeywordBasedTableSelector implements TableSelector {

    // 常见的业务关键词映射
    private static final Map<String, List<String>> BUSINESS_KEYWORDS = new HashMap<>();
    
    static {
        // 用户相关
        BUSINESS_KEYWORDS.put("用户", Arrays.asList("user", "customer", "member", "account", "person"));
        BUSINESS_KEYWORDS.put("学生", Arrays.asList("student", "pupil", "learner"));
        BUSINESS_KEYWORDS.put("员工", Arrays.asList("employee", "staff", "worker"));
        
        // 订单相关
        BUSINESS_KEYWORDS.put("订单", Arrays.asList("order", "purchase", "transaction"));
        BUSINESS_KEYWORDS.put("商品", Arrays.asList("product", "item", "goods", "merchandise"));
        
        // 成绩相关
        BUSINESS_KEYWORDS.put("成绩", Arrays.asList("score", "grade", "mark", "result"));
        BUSINESS_KEYWORDS.put("考试", Arrays.asList("exam", "test", "quiz"));
        
        // 时间相关
        BUSINESS_KEYWORDS.put("时间", Arrays.asList("time", "date", "created", "updated", "modified"));
        BUSINESS_KEYWORDS.put("日期", Arrays.asList("date", "day", "month", "year"));
    }

    @Override
    public TableSelectionResult selectRelevantTables(String question, List<TableDTO> allTables, Map<String, List<ColumnDTO>> allColumns) {
        log.info("开始基于关键词选择相关表，问题：{}", question);
        
        // 1. 提取问题中的关键词
        Set<String> questionKeywords = extractKeywords(question.toLowerCase());
        log.debug("提取到的关键词：{}", questionKeywords);
        
        // 2. 为每个表计算相关性得分
        Map<TableDTO, Double> tableScores = new HashMap<>();
        Map<TableDTO, String> tableReasons = new HashMap<>();
        
        for (TableDTO table : allTables) {
            double score = calculateTableRelevance(table, allColumns.get(table.getId()), questionKeywords);
            if (score > 0) {
                tableScores.put(table, score);
                tableReasons.put(table, generateReason(table, allColumns.get(table.getId()), questionKeywords));
            }
        }
        
        // 3. 选择得分最高的表（最多选择5个表以控制token）
        List<TableDTO> selectedTables = tableScores.entrySet().stream()
                .sorted(Map.Entry.<TableDTO, Double>comparingByValue().reversed())
                .limit(5)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        // 4. 构建选中表的字段映射
        Map<String, List<ColumnDTO>> selectedColumns = new HashMap<>();
        for (TableDTO table : selectedTables) {
            selectedColumns.put(table.getId(), allColumns.get(table.getId()));
        }
        
        // 5. 计算整体置信度
        double confidence = calculateOverallConfidence(tableScores, selectedTables);
        
        // 6. 生成选择原因
        String reason = generateSelectionReason(selectedTables, tableReasons);
        
        log.info("选择了{}个表，置信度：{:.2f}", selectedTables.size(), confidence);
        log.debug("选择的表：{}", selectedTables.stream().map(TableDTO::getName).collect(Collectors.toList()));
        
        return new TableSelectionResult(selectedTables, selectedColumns, confidence, reason);
    }
    
    /**
     * 从问题中提取关键词
     */
    private Set<String> extractKeywords(String question) {
        Set<String> keywords = new HashSet<>();
        
        // 直接匹配业务关键词
        for (Map.Entry<String, List<String>> entry : BUSINESS_KEYWORDS.entrySet()) {
            if (question.contains(entry.getKey())) {
                keywords.add(entry.getKey());
                keywords.addAll(entry.getValue());
            }
        }
        
        // 提取可能的表名和字段名（简单的词汇提取）
        String[] words = question.split("[\\s，。！？、]+");
        for (String word : words) {
            if (word.length() >= 2) {
                keywords.add(word.toLowerCase());
            }
        }
        
        return keywords;
    }
    
    /**
     * 计算表的相关性得分
     */
    private double calculateTableRelevance(TableDTO table, List<ColumnDTO> columns, Set<String> questionKeywords) {
        double score = 0.0;
        
        // 表名匹配（权重最高）
        String tableName = table.getName().toLowerCase();
        for (String keyword : questionKeywords) {
            if (tableName.contains(keyword) || keyword.contains(tableName)) {
                score += 10.0;
            }
        }
        
        // 表描述匹配
        if (table.getDescription() != null) {
            String tableDesc = table.getDescription().toLowerCase();
            for (String keyword : questionKeywords) {
                if (tableDesc.contains(keyword)) {
                    score += 5.0;
                }
            }
        }
        
        // 字段名匹配
        if (columns != null) {
            for (ColumnDTO column : columns) {
                String columnName = column.getName().toLowerCase();
                for (String keyword : questionKeywords) {
                    if (columnName.contains(keyword) || keyword.contains(columnName)) {
                        score += 3.0;
                    }
                }
                
                // 字段描述匹配
                if (column.getDescription() != null) {
                    String columnDesc = column.getDescription().toLowerCase();
                    for (String keyword : questionKeywords) {
                        if (columnDesc.contains(keyword)) {
                            score += 1.0;
                        }
                    }
                }
            }
        }
        
        return score;
    }
    
    /**
     * 生成表选择的原因
     */
    private String generateReason(TableDTO table, List<ColumnDTO> columns, Set<String> questionKeywords) {
        List<String> reasons = new ArrayList<>();
        
        // 检查表名匹配
        String tableName = table.getName().toLowerCase();
        for (String keyword : questionKeywords) {
            if (tableName.contains(keyword) || keyword.contains(tableName)) {
                reasons.add("表名包含关键词: " + keyword);
            }
        }
        
        // 检查字段匹配
        if (columns != null) {
            Set<String> matchedColumns = new HashSet<>();
            for (ColumnDTO column : columns) {
                String columnName = column.getName().toLowerCase();
                for (String keyword : questionKeywords) {
                    if (columnName.contains(keyword) || keyword.contains(columnName)) {
                        matchedColumns.add(column.getName());
                    }
                }
            }
            if (!matchedColumns.isEmpty()) {
                reasons.add("字段匹配: " + String.join(", ", matchedColumns));
            }
        }
        
        return String.join("; ", reasons);
    }
    
    /**
     * 计算整体置信度
     */
    private double calculateOverallConfidence(Map<TableDTO, Double> tableScores, List<TableDTO> selectedTables) {
        if (selectedTables.isEmpty()) {
            return 0.0;
        }
        
        double totalScore = selectedTables.stream()
                .mapToDouble(table -> tableScores.getOrDefault(table, 0.0))
                .sum();
        
        // 归一化到0-1之间
        return Math.min(1.0, totalScore / (selectedTables.size() * 10.0));
    }
    
    /**
     * 生成选择原因说明
     */
    private String generateSelectionReason(List<TableDTO> selectedTables, Map<TableDTO, String> tableReasons) {
        if (selectedTables.isEmpty()) {
            return "未找到相关表";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("基于关键词匹配选择了以下表：\n");
        for (TableDTO table : selectedTables) {
            sb.append("- ").append(table.getName());
            String reason = tableReasons.get(table);
            if (reason != null && !reason.isEmpty()) {
                sb.append(" (").append(reason).append(")");
            }
            sb.append("\n");
        }
        
        return sb.toString();
    }
}
