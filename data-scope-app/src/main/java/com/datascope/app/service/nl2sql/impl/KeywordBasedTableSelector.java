package com.datascope.app.service.nl2sql.impl;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.TableSelector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于关键词匹配的表选择器实现
 * 通过分析自然语言中的关键词来匹配相关的表和字段
 */
@Slf4j
@Service
public class KeywordBasedTableSelector implements TableSelector {

    // 常见的业务关键词映射
    private static final Map<String, List<String>> BUSINESS_KEYWORDS = new HashMap<>();

    // 表关联关系映射（当选中某个表时，可能需要的关联表）
    private static final Map<String, List<String>> TABLE_RELATIONSHIPS = new HashMap<>();

    static {
        // 用户相关
        BUSINESS_KEYWORDS.put("用户", Arrays.asList("user", "customer", "member", "account", "person", "client"));
        BUSINESS_KEYWORDS.put("学生", Arrays.asList("student", "pupil", "learner", "stu"));
        BUSINESS_KEYWORDS.put("员工", Arrays.asList("employee", "staff", "worker", "emp"));
        BUSINESS_KEYWORDS.put("客户", Arrays.asList("customer", "client", "user", "buyer"));

        // 订单相关
        BUSINESS_KEYWORDS.put("订单", Arrays.asList("order", "purchase", "transaction", "buy"));
        BUSINESS_KEYWORDS.put("商品", Arrays.asList("product", "item", "goods", "merchandise", "prod"));
        BUSINESS_KEYWORDS.put("销售", Arrays.asList("sale", "sell", "revenue", "sales"));

        // 成绩相关
        BUSINESS_KEYWORDS.put("成绩", Arrays.asList("score", "grade", "mark", "result", "achievement"));
        BUSINESS_KEYWORDS.put("考试", Arrays.asList("exam", "test", "quiz", "examination"));
        BUSINESS_KEYWORDS.put("课程", Arrays.asList("course", "subject", "class", "lesson"));

        // 部门组织相关
        BUSINESS_KEYWORDS.put("部门", Arrays.asList("department", "dept", "division", "team"));
        BUSINESS_KEYWORDS.put("公司", Arrays.asList("company", "corp", "organization", "firm"));

        // 时间相关
        BUSINESS_KEYWORDS.put("时间", Arrays.asList("time", "date", "created", "updated", "modified"));
        BUSINESS_KEYWORDS.put("日期", Arrays.asList("date", "day", "month", "year"));

        // 统计相关
        BUSINESS_KEYWORDS.put("数量", Arrays.asList("count", "number", "amount", "quantity"));
        BUSINESS_KEYWORDS.put("总计", Arrays.asList("total", "sum", "aggregate"));
        BUSINESS_KEYWORDS.put("平均", Arrays.asList("average", "avg", "mean"));
        BUSINESS_KEYWORDS.put("最大", Arrays.asList("max", "maximum", "highest", "top"));
        BUSINESS_KEYWORDS.put("最小", Arrays.asList("min", "minimum", "lowest", "bottom"));

        // 支付行业核心词汇
        BUSINESS_KEYWORDS.put("支付", Arrays.asList("payment", "pay", "settle", "pmt"));
        BUSINESS_KEYWORDS.put("收款", Arrays.asList("receive", "collection", "collect", "recv"));
        BUSINESS_KEYWORDS.put("代付", Arrays.asList("payout", "disbursement", "remittance"));
        BUSINESS_KEYWORDS.put("代收", Arrays.asList("collection", "gather", "collect"));
        BUSINESS_KEYWORDS.put("充值", Arrays.asList("recharge", "topup", "load"));
        BUSINESS_KEYWORDS.put("提现", Arrays.asList("withdraw", "cashout", "redeem"));
        BUSINESS_KEYWORDS.put("退款", Arrays.asList("refund", "return", "chargeback"));
        BUSINESS_KEYWORDS.put("结算", Arrays.asList("settlement", "settle", "clearing"));
        BUSINESS_KEYWORDS.put("清算", Arrays.asList("clearing", "clear"));

        // 金融机构
        BUSINESS_KEYWORDS.put("银行", Arrays.asList("bank", "banking"));
        BUSINESS_KEYWORDS.put("银联", Arrays.asList("unionpay", "cup"));
        BUSINESS_KEYWORDS.put("网联", Arrays.asList("nucc", "wanglian"));
        BUSINESS_KEYWORDS.put("央行", Arrays.asList("pboc", "centralbank"));
        BUSINESS_KEYWORDS.put("第三方", Arrays.asList("thirdparty", "3rd"));

        // 交易类型
        BUSINESS_KEYWORDS.put("交易", Arrays.asList("transaction", "trade", "deal", "trans", "txn"));
        BUSINESS_KEYWORDS.put("入金", Arrays.asList("deposit", "credit", "inbound", "in"));
        BUSINESS_KEYWORDS.put("出金", Arrays.asList("withdrawal", "debit", "outbound", "out"));
        BUSINESS_KEYWORDS.put("转账", Arrays.asList("transfer", "remit"));
        BUSINESS_KEYWORDS.put("冲正", Arrays.asList("reversal", "reverse"));
        BUSINESS_KEYWORDS.put("冲退", Arrays.asList("void", "cancel"));

        // 账户相关
        BUSINESS_KEYWORDS.put("账户", Arrays.asList("account", "acc"));
        BUSINESS_KEYWORDS.put("余额", Arrays.asList("balance", "amount"));
        BUSINESS_KEYWORDS.put("冻结", Arrays.asList("freeze", "frozen", "hold"));
        BUSINESS_KEYWORDS.put("解冻", Arrays.asList("unfreeze", "release"));
        BUSINESS_KEYWORDS.put("虚户", Arrays.asList("virtual", "sub"));

        // 渠道相关
        BUSINESS_KEYWORDS.put("渠道", Arrays.asList("channel", "gateway", "route"));
        BUSINESS_KEYWORDS.put("通道", Arrays.asList("channel", "route", "path"));
        BUSINESS_KEYWORDS.put("接口", Arrays.asList("interface", "api", "endpoint"));
        BUSINESS_KEYWORDS.put("商户", Arrays.asList("merchant", "mch", "partner"));

        // 状态相关
        BUSINESS_KEYWORDS.put("成功", Arrays.asList("success", "succ", "ok"));
        BUSINESS_KEYWORDS.put("失败", Arrays.asList("fail", "failed", "error"));
        BUSINESS_KEYWORDS.put("处理中", Arrays.asList("processing", "pending", "wait"));
        BUSINESS_KEYWORDS.put("超时", Arrays.asList("timeout", "expired"));

        // 流水记录
        BUSINESS_KEYWORDS.put("流水", Arrays.asList("flow", "record", "log", "history", "journal"));
        BUSINESS_KEYWORDS.put("对账", Arrays.asList("reconcile", "recon", "check"));
        BUSINESS_KEYWORDS.put("差错", Arrays.asList("exception", "error", "diff"));
        BUSINESS_KEYWORDS.put("调账", Arrays.asList("adjust", "correction"));

        // 数据相关
        BUSINESS_KEYWORDS.put("数据", Arrays.asList("data", "info", "record"));
        BUSINESS_KEYWORDS.put("记录", Arrays.asList("record", "log", "entry"));
        BUSINESS_KEYWORDS.put("信息", Arrays.asList("info", "information", "detail"));

        // 支付业务表关联关系
        TABLE_RELATIONSHIPS.put("payment", Arrays.asList("merchant", "account", "channel", "order"));
        TABLE_RELATIONSHIPS.put("transaction", Arrays.asList("account", "merchant", "channel", "settlement"));
        TABLE_RELATIONSHIPS.put("merchant", Arrays.asList("account", "channel", "settlement", "rate"));
        TABLE_RELATIONSHIPS.put("account", Arrays.asList("balance", "freeze", "flow", "journal"));
        TABLE_RELATIONSHIPS.put("channel", Arrays.asList("route", "gateway", "bank"));
        TABLE_RELATIONSHIPS.put("settlement", Arrays.asList("merchant", "account", "reconcile"));
        TABLE_RELATIONSHIPS.put("reconcile", Arrays.asList("settlement", "exception", "diff"));
        TABLE_RELATIONSHIPS.put("refund", Arrays.asList("payment", "transaction", "merchant"));

        // 通用业务表关联关系
        TABLE_RELATIONSHIPS.put("order", Arrays.asList("user", "customer", "product", "item"));
        TABLE_RELATIONSHIPS.put("student", Arrays.asList("score", "grade", "course", "class"));
        TABLE_RELATIONSHIPS.put("employee", Arrays.asList("department", "dept", "team"));
    }

    @Override
    public TableSelectionResult selectRelevantTables(String question, List<TableDTO> allTables, Map<String, List<ColumnDTO>> allColumns) {
        log.info("开始基于关键词选择相关表，问题：{}", question);

        // 1. 提取问题中的关键词
        Set<String> questionKeywords = extractKeywords(question.toLowerCase());
        log.debug("提取到的关键词：{}", questionKeywords);

        // 2. 为每个表计算相关性得分
        Map<TableDTO, Double> tableScores = new HashMap<>();
        Map<TableDTO, String> tableReasons = new HashMap<>();

        for (TableDTO table : allTables) {
            double score = calculateTableRelevance(table, allColumns.get(table.getId()), questionKeywords);
            if (score > 0) {
                tableScores.put(table, score);
                tableReasons.put(table, generateReason(table, allColumns.get(table.getId()), questionKeywords));
            }
        }

        // 3. 选择得分最高的表（最多选择6个表以控制token）
        List<TableDTO> selectedTables = tableScores.entrySet().stream()
                .sorted(Map.Entry.<TableDTO, Double>comparingByValue().reversed())
                .limit(6)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 4. 添加关联表（如果置信度较高且表数量较少）
        if (!selectedTables.isEmpty() && selectedTables.size() <= 3) {
            selectedTables = addRelatedTables(selectedTables, allTables, tableScores);
        }

        // 5. 构建选中表的字段映射
        Map<String, List<ColumnDTO>> selectedColumns = new HashMap<>();
        for (TableDTO table : selectedTables) {
            selectedColumns.put(table.getId(), allColumns.get(table.getId()));
        }

        // 6. 计算整体置信度
        double confidence = calculateOverallConfidence(tableScores, selectedTables);

        // 7. 生成选择原因
        String reason = generateSelectionReason(selectedTables, tableReasons, confidence);

        log.info("选择了{}个表，置信度：{:.2f}", selectedTables.size(), confidence);
        log.debug("选择的表：{}", selectedTables.stream().map(TableDTO::getName).collect(Collectors.toList()));

        return new TableSelectionResult(selectedTables, selectedColumns, confidence, reason);
    }

    /**
     * 从问题中提取关键词
     */
    private Set<String> extractKeywords(String question) {
        Set<String> keywords = new HashSet<>();

        // 直接匹配业务关键词
        for (Map.Entry<String, List<String>> entry : BUSINESS_KEYWORDS.entrySet()) {
            if (question.contains(entry.getKey())) {
                keywords.add(entry.getKey());
                keywords.addAll(entry.getValue());
            }
        }

        // 提取可能的表名和字段名（简单的词汇提取）
        String[] words = question.split("[\\s，。！？、；：\"\"''（）\\(\\)\\[\\]]+");
        for (String word : words) {
            if (word.length() >= 2 && !isStopWord(word)) {
                keywords.add(word.toLowerCase());
            }
        }

        return keywords;
    }

    /**
     * 判断是否为停用词
     */
    private boolean isStopWord(String word) {
        Set<String> stopWords = new HashSet<>(Arrays.asList(
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这",
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "from", "up", "about", "into", "through", "during", "before", "after", "above", "below", "between", "among", "within", "without", "against", "toward", "upon", "beneath", "beside", "behind", "beyond"
        ));
        return stopWords.contains(word.toLowerCase());
    }

    /**
     * 计算表的相关性得分
     */
    private double calculateTableRelevance(TableDTO table, List<ColumnDTO> columns, Set<String> questionKeywords) {
        double score = 0.0;
        String tableName = table.getName().toLowerCase();

        // 表名匹配（权重最高）
        for (String keyword : questionKeywords) {
            if (tableName.contains(keyword) || keyword.contains(tableName)) {
                // 支付核心词汇给予更高权重
                double weight = getKeywordWeight(keyword);
                score += weight;
                log.debug("表名匹配: {} <-> {}, 得分+{}", tableName, keyword, weight);
            }
        }

        // 表描述匹配
        if (table.getDescription() != null) {
            String tableDesc = table.getDescription().toLowerCase();
            for (String keyword : questionKeywords) {
                if (tableDesc.contains(keyword)) {
                    double weight = getKeywordWeight(keyword) * 0.5; // 描述匹配权重为表名的一半
                    score += weight;
                    log.debug("表描述匹配: {} <-> {}, 得分+{}", tableDesc, keyword, weight);
                }
            }
        }

        // 字段名匹配
        if (columns != null) {
            for (ColumnDTO column : columns) {
                String columnName = column.getName().toLowerCase();
                for (String keyword : questionKeywords) {
                    if (columnName.contentEquals(keyword) || keyword.contains(columnName)) {
                        score += 3.0;
                        log.debug("字段名匹配: {} <-> {}, 得分+3", columnName, keyword);
                    }
                }

                // 字段描述匹配
                if (column.getDescription() != null) {
                    String columnDesc = column.getDescription().toLowerCase();
                    for (String keyword : questionKeywords) {
                        if (columnDesc.contains(keyword)) {
                            score += 1.0;
                            log.debug("字段描述匹配: {} <-> {}, 得分+1", columnDesc, keyword);
                        }
                    }
                }
            }
        }

        log.debug("表 {} 总得分: {}", table.getName(), score);
        return score;
    }

    /**
     * 添加关联表
     */
    private List<TableDTO> addRelatedTables(List<TableDTO> selectedTables, List<TableDTO> allTables, Map<TableDTO, Double> tableScores) {
        Set<TableDTO> result = new LinkedHashSet<>(selectedTables);

        for (TableDTO selectedTable : selectedTables) {
            String tableName = selectedTable.getName().toLowerCase();

            // 查找可能的关联表
            for (Map.Entry<String, List<String>> entry : TABLE_RELATIONSHIPS.entrySet()) {
                if (tableName.contains(entry.getKey())) {
                    for (String relatedKeyword : entry.getValue()) {
                        for (TableDTO table : allTables) {
                            if (table.getName().toLowerCase().contains(relatedKeyword) && !result.contains(table)) {
                                result.add(table);
                                log.debug("添加关联表: {} -> {}", selectedTable.getName(), table.getName());
                                // 限制总表数
                                if (result.size() >= 8) {
                                    return new ArrayList<>(result);
                                }
                            }
                        }
                    }
                }
            }
        }

        return new ArrayList<>(result);
    }

    /**
     * 生成表选择的原因
     */
    private String generateReason(TableDTO table, List<ColumnDTO> columns, Set<String> questionKeywords) {
        List<String> reasons = new ArrayList<>();

        // 检查表名匹配
        String tableName = table.getName().toLowerCase();
        for (String keyword : questionKeywords) {
            if (tableName.contains(keyword) || keyword.contains(tableName)) {
                reasons.add("表名包含关键词: " + keyword);
            }
        }

        // 检查字段匹配
        if (columns != null) {
            Set<String> matchedColumns = new HashSet<>();
            for (ColumnDTO column : columns) {
                String columnName = column.getName().toLowerCase();
                for (String keyword : questionKeywords) {
                    if (columnName.contains(keyword) || keyword.contains(columnName)) {
                        matchedColumns.add(column.getName());
                    }
                }
            }
            if (!matchedColumns.isEmpty()) {
                reasons.add("字段匹配: " + String.join(", ", matchedColumns));
            }
        }

        return String.join("; ", reasons);
    }

    /**
     * 计算整体置信度
     */
    private double calculateOverallConfidence(Map<TableDTO, Double> tableScores, List<TableDTO> selectedTables) {
        if (selectedTables.isEmpty()) {
            return 0.0;
        }

        double totalScore = selectedTables.stream()
                .mapToDouble(table -> tableScores.getOrDefault(table, 0.0))
                .sum();

        double maxPossibleScore = selectedTables.size() * 15.0; // 假设每个表最高15分
        double confidence = Math.min(1.0, totalScore / maxPossibleScore);

        // 根据选择的表数量调整置信度
        if (selectedTables.size() == 1 && totalScore >= 10.0) {
            confidence = Math.max(confidence, 0.8); // 单表高分匹配
        } else if (selectedTables.size() <= 3 && totalScore >= 20.0) {
            confidence = Math.max(confidence, 0.7); // 少量表高分匹配
        }

        return confidence;
    }

    /**
     * 生成选择原因说明
     */
    private String generateSelectionReason(List<TableDTO> selectedTables, Map<TableDTO, String> tableReasons, double confidence) {
        if (selectedTables.isEmpty()) {
            return "未找到相关表，建议手动指定contextTables参数";
        }

        StringBuilder sb = new StringBuilder();
        sb.append(String.format("基于关键词匹配选择了%d个表 (置信度: %.1f%%)：\n", selectedTables.size(), confidence * 100));

        for (int i = 0; i < selectedTables.size(); i++) {
            TableDTO table = selectedTables.get(i);
            sb.append(String.format("%d. %s", i + 1, table.getName()));
            String reason = tableReasons.get(table);
            if (reason != null && !reason.isEmpty()) {
                sb.append(" (").append(reason).append(")");
            }
            sb.append("\n");
        }

        // 添加置信度建议
        if (confidence < 0.5) {
            sb.append("\n⚠️ 置信度较低，建议检查选择结果或手动指定相关表");
        } else if (confidence < 0.7) {
            sb.append("\n💡 置信度中等，如果结果不准确可手动指定相关表");
        } else {
            sb.append("\n✅ 置信度较高，预期能生成准确的SQL");
        }

        return sb.toString();
    }

    /**
     * 根据关键词获取权重
     * 支付核心业务词汇给予更高权重
     */
    private double getKeywordWeight(String keyword) {
        // 支付核心词汇 - 最高权重
        Set<String> corePaymentKeywords = new HashSet<>(Arrays.asList(
            "payment", "pay", "transaction", "trans", "txn", "settle", "settlement",
            "支付", "交易", "结算", "清算"
        ));

        // 支付业务词汇 - 高权重
        Set<String> paymentBusinessKeywords = new HashSet<>(Arrays.asList(
            "merchant", "channel", "account", "refund", "recharge", "withdraw",
            "商户", "渠道", "账户", "退款", "充值", "提现", "代付", "代收"
        ));

        // 金融机构词汇 - 中高权重
        Set<String> financialKeywords = new HashSet<>(Arrays.asList(
            "bank", "banking", "unionpay", "nucc", "pboc",
            "银行", "银联", "网联", "央行"
        ));

        // 状态和流程词汇 - 中等权重
        Set<String> statusKeywords = new HashSet<>(Arrays.asList(
            "success", "fail", "processing", "pending", "reconcile",
            "成功", "失败", "处理中", "对账", "流水"
        ));

        if (corePaymentKeywords.contains(keyword.toLowerCase())) {
            return 15.0; // 核心支付词汇最高权重
        } else if (paymentBusinessKeywords.contains(keyword.toLowerCase())) {
            return 12.0; // 支付业务词汇高权重
        } else if (financialKeywords.contains(keyword.toLowerCase())) {
            return 10.0; // 金融机构词汇中高权重
        } else if (statusKeywords.contains(keyword.toLowerCase())) {
            return 8.0;  // 状态词汇中等权重
        } else {
            return 6.0;  // 其他词汇基础权重
        }
    }
}
