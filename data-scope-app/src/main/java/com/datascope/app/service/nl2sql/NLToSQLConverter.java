package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.query.NaturalLanguageQueryParams;
import com.datascope.app.service.QueryService.NaturalLanguageToSqlResult;

/**
 * 自然语言到SQL转换器接口
 * 负责将自然语言查询转换为SQL语句
 */
public interface NLToSQLConverter {

    /**
     * 将自然语言查询转换为SQL
     *
     * @param params 自然语言查询参数
     * @return 转换结果，包含SQL语句、解释和相关表
     */
    NaturalLanguageToSqlResult convertToSQL(NaturalLanguageQueryParams params);
}