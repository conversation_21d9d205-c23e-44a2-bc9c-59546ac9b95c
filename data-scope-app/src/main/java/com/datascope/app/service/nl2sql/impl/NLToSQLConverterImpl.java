package com.datascope.app.service.nl2sql.impl;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.dto.query.NaturalLanguageQueryParams;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.service.MetadataService;
import com.datascope.app.service.QueryService.NaturalLanguageToSqlResult;
import com.datascope.app.service.llm.LLMIntegrationService;
import com.datascope.app.service.nl2sql.NLToSQLConverter;
import com.datascope.app.service.nl2sql.PromptGenerator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 自然语言到SQL转换器实现类
 * 负责将自然语言查询转换为SQL语句
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NLToSQLConverterImpl implements NLToSQLConverter {

    private final LLMIntegrationService llmIntegrationService;
    private final PromptGenerator promptGenerator;
    private final MetadataService metadataService;
    private final DatasourceMapper datasourceMapper;
    private final SchemaMapper schemaMapper;

    // 用于提取SQL的正则表达式
    private static final Pattern SQL_PATTERN = Pattern.compile("```sql\\s*([\\s\\S]*?)\\s*```");
    // 用于提取解释的正则表达式
    private static final Pattern EXPLANATION_PATTERN = Pattern.compile("解释：\\s*([\\s\\S]*?)\\s*$");

    @Override
    public NaturalLanguageToSqlResult convertToSQL(NaturalLanguageQueryParams params) {
        log.info("开始处理自然语言转SQL请求: {}", params);

        String question = params.getQuestion();
        String dataSourceId = params.getDataSourceId();
        List<String> contextTables = params.getContextTables();

        // 检查参数
        if (question == null || question.trim().isEmpty()) {
            log.error("问题为空");
            throw new RuntimeException("问题不能为空");
        }

        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(dataSourceId);
        if (datasource == null) {
            log.error("找不到数据源: {}", dataSourceId);
            throw new RuntimeException("找不到数据源: " + dataSourceId);
        }

        log.info("使用数据源: {}({})", datasource.getName(), dataSourceId);

        // 获取数据源的schema列表
        List<Schema> schemas = getSchemas(dataSourceId);
        if (schemas.isEmpty()) {
            log.error("数据源没有可用的schema: {}", dataSourceId);
            throw new RuntimeException("数据源没有可用的schema");
        }

        // 获取表和列信息
        List<TableDTO> tables = new ArrayList<>();
        Map<String, List<ColumnDTO>> columnsMap = new HashMap<>();

        // 如果指定了上下文表，则只获取这些表的信息
        if (contextTables != null && !contextTables.isEmpty()) {
            log.info("使用指定的上下文表: {}", contextTables);
            for (Schema schema : schemas) {
                List<TableDTO> schemaTables = metadataService.getTables(schema.getId());
                for (TableDTO table : schemaTables) {
                    if (contextTables.contains(table.getName())) {
                        tables.add(table);
                        List<ColumnDTO> columns = metadataService.getColumns(table.getId());
                        columnsMap.put(table.getId(), columns);
                    }
                }
            }
        } else {
            // 否则获取所有表的信息
            log.info("未指定上下文表，使用所有可用表");
            for (Schema schema : schemas) {
                List<TableDTO> schemaTables = metadataService.getTables(schema.getId());
                tables.addAll(schemaTables);
                for (TableDTO table : schemaTables) {
                    List<ColumnDTO> columns = metadataService.getColumns(table.getId());
                    columnsMap.put(table.getId(), columns);
                }
            }
        }

        if (tables.isEmpty()) {
            log.error("没有可用的表信息");
            throw new RuntimeException("没有可用的表信息");
        }

        log.info("获取到{}个表的信息", tables.size());

        // 生成提示并调用LLM
        String prompt = promptGenerator.generateNL2SQLPrompt(question, tables, columnsMap);
        log.debug("生成的提示: {}", prompt);

        // 调用LLM获取响应
        String llmResponse = llmIntegrationService.sendPrompt(prompt);
        log.debug("LLM响应: {}", llmResponse);

        // 解析LLM响应
        String sql = extractSQL(llmResponse);
        String explanation = extractExplanation(llmResponse);
        List<String> usedTables = extractUsedTables(sql, tables);

        log.info("自然语言转SQL结果: SQL={}, 表={}, 解释={}", sql, usedTables, explanation);

        return NaturalLanguageToSqlResult.builder()
                .sql(sql)
                .explanation(explanation)
                .tables(usedTables)
                .build();
    }

    /**
     * 获取数据源的schema列表
     */
    private List<Schema> getSchemas(String dataSourceId) {
        LambdaQueryWrapper<Schema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Schema::getDatasourceId, dataSourceId);
        return schemaMapper.selectList(queryWrapper);
    }

    /**
     * 从LLM响应中提取SQL语句
     */
    private String extractSQL(String llmResponse) {
        Matcher matcher = SQL_PATTERN.matcher(llmResponse);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        // 如果没有找到SQL代码块，尝试直接返回整个响应
        return llmResponse.trim();
    }

    /**
     * 从LLM响应中提取解释
     */
    private String extractExplanation(String llmResponse) {
        Matcher matcher = EXPLANATION_PATTERN.matcher(llmResponse);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return "";
    }

    /**
     * 从SQL中提取使用的表名
     */
    private List<String> extractUsedTables(String sql, List<TableDTO> availableTables) {
        List<String> usedTables = new ArrayList<>();
        for (TableDTO table : availableTables) {
            String tableName = table.getName();
            // 简单检查SQL中是否包含表名
            if (sql.contains(tableName)) {
                // 进一步检查是否是作为表名使用（前后有空格、逗号、括号等）
                String regex = "\\b" + tableName + "\\b";
                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(sql);
                if (matcher.find()) {
                    usedTables.add(tableName);
                }
            }
        }
        return usedTables;
    }
}