package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;

import java.util.List;
import java.util.Map;

/**
 * 表选择器接口
 * 负责根据自然语言问题智能选择相关的表和字段，减少token消耗
 */
public interface TableSelector {

    /**
     * 根据自然语言问题选择相关的表
     *
     * @param question 自然语言问题
     * @param allTables 所有可用的表
     * @param allColumns 所有表的字段信息
     * @return 选择结果
     */
    TableSelectionResult selectRelevantTables(String question, List<TableDTO> allTables, Map<String, List<ColumnDTO>> allColumns);

    /**
     * 表选择结果
     */
    class TableSelectionResult {
        private List<TableDTO> selectedTables;
        private Map<String, List<ColumnDTO>> selectedColumns;
        private double confidence; // 置信度
        private String reason; // 选择原因

        public TableSelectionResult(List<TableDTO> selectedTables, Map<String, List<ColumnDTO>> selectedColumns, double confidence, String reason) {
            this.selectedTables = selectedTables;
            this.selectedColumns = selectedColumns;
            this.confidence = confidence;
            this.reason = reason;
        }

        // Getters
        public List<TableDTO> getSelectedTables() { return selectedTables; }
        public Map<String, List<ColumnDTO>> getSelectedColumns() { return selectedColumns; }
        public double getConfidence() { return confidence; }
        public String getReason() { return reason; }
    }
}
