package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import lombok.Data;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 表选择器接口
 * 负责根据自然语言问题智能选择相关的表和字段，减少token消耗
 */
public interface TableSelector {

    /**
     * 根据自然语言问题选择相关的表
     *
     * @param question 自然语言问题
     * @param allTables 所有可用的表
     * @param allColumns 所有表的字段信息
     * @return 选择结果
     */
    TableSelectionResult selectRelevantTables(String question, List<TableDTO> allTables, Map<String, List<ColumnDTO>> allColumns);

    /**
     * 表选择结果
     */
    @Data
    @AllArgsConstructor
    class TableSelectionResult {
        private List<TableDTO> selectedTables;
        private Map<String, List<ColumnDTO>> selectedColumns;
        private double confidence; // 置信度
        private String reason; // 选择原因
    }
}
