package com.datascope.app.service.nl2sql.impl;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.PromptGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 提示生成器实现类
 * 负责生成发送给LLM的提示内容
 */
@Slf4j
@Service
public class PromptGeneratorImpl implements PromptGenerator {

    private static final String PROMPT_TEMPLATE =
            "你是一个SQL专家，请将以下自然语言问题转换为SQL查询语句。\n" +
            "问题：%s\n\n" +
            "数据库表结构：\n%s\n" +
            "请生成一个有效的SQL查询语句，满足以下要求：\n" +
            "1. 使用标准SQL语法\n" +
            "2. 只使用上述提供的表和列\n" +
            "3. 确保查询语句语法正确且能够执行\n" +
            "4. 返回的SQL应该能够回答用户的问题\n" +
            "5. 不要使用未在表结构中定义的表或列\n" +
            "6. 如果问题中提到了特定的限制条件，请在SQL中体现\n\n" +
            "请按以下格式返回：\n" +
            "```sql\n" +
            "你的SQL查询语句\n" +
            "```\n\n" +
            "解释：\n" +
            "简要解释你的SQL查询如何解决问题";

    private static final String PROMPT_TEMPLATE_WITH_RELATIONSHIPS =
            "你是一个SQL专家，请将以下自然语言问题转换为SQL查询语句。\n" +
            "问题：%s\n\n" +
            "数据库表结构：\n%s\n" +
            "表关系：\n%s\n" +
            "请生成一个有效的SQL查询语句，满足以下要求：\n" +
            "1. 使用标准SQL语法\n" +
            "2. 只使用上述提供的表和列\n" +
            "3. 确保查询语句语法正确且能够执行\n" +
            "4. 返回的SQL应该能够回答用户的问题\n" +
            "5. 不要使用未在表结构中定义的表或列\n" +
            "6. 如果问题中提到了特定的限制条件，请在SQL中体现\n" +
            "7. 使用提供的表关系信息来正确连接表\n\n" +
            "请按以下格式返回：\n" +
            "```sql\n" +
            "你的SQL查询语句\n" +
            "```\n\n" +
            "解释：\n" +
            "简要解释你的SQL查询如何解决问题";

    @Override
    public String generateNL2SQLPrompt(String question, List<TableDTO> tables, Map<String, List<ColumnDTO>> columns) {
        String tableStructure = formatTableStructure(tables, columns);
        return String.format(PROMPT_TEMPLATE, question, tableStructure);
    }

    @Override
    public String generateNL2SQLPrompt(String question, List<TableDTO> tables,
                                      Map<String, List<ColumnDTO>> columns,
                                      Map<String, List<String>> tableRelationships) {
        String tableStructure = formatTableStructure(tables, columns);
        String relationships = formatTableRelationships(tableRelationships);
        return String.format(PROMPT_TEMPLATE_WITH_RELATIONSHIPS, question, tableStructure, relationships);
    }

    /**
     * 格式化表结构信息
     */
    private String formatTableStructure(List<TableDTO> tables, Map<String, List<ColumnDTO>> columns) {
        StringBuilder sb = new StringBuilder();

        for (TableDTO table : tables) {
            sb.append("表名: ").append(table.getName());
            if (table.getDescription() != null && !table.getDescription().isEmpty()) {
                sb.append(" (描述: ").append(table.getDescription()).append(")");
            }
            sb.append("\n");

            List<ColumnDTO> tableColumns = columns.get(table.getId());
            if (tableColumns != null && !tableColumns.isEmpty()) {
                sb.append("列: \n");
                for (ColumnDTO column : tableColumns) {
                    sb.append("  - ").append(column.getName())
                      .append(" (").append(column.getDataType()).append(")")
                      .append(column.getIsPrimaryKey() != null && column.getIsPrimaryKey() ? " [主键]" : "")
                      .append(column.getIsNullable() != null && !column.getIsNullable() ? " [非空]" : "");

                    if (column.getDescription() != null && !column.getDescription().isEmpty()) {
                        sb.append(" 描述: ").append(column.getDescription());
                    }
                    sb.append("\n");
                }
            } else {
                sb.append("列信息不可用\n");
            }
            sb.append("\n");
        }

        return sb.toString();
    }

    /**
     * 格式化表关系信息
     */
    private String formatTableRelationships(Map<String, List<String>> tableRelationships) {
        if (tableRelationships == null || tableRelationships.isEmpty()) {
            return "无表关系信息";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<String>> entry : tableRelationships.entrySet()) {
            String tableName = entry.getKey();
            List<String> relations = entry.getValue();

            sb.append(tableName).append(" 关联: ");
            sb.append(String.join(", ", relations)).append("\n");
        }

        return sb.toString();
    }
}
