package com.datascope.app.service.nl2sql;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;

import java.util.List;
import java.util.Map;

/**
 * 提示生成器接口
 * 负责生成发送给LLM的提示内容
 */
public interface PromptGenerator {

    /**
     * 生成自然语言转SQL的提示
     *
     * @param question 用户问题
     * @param tables 相关表信息列表
     * @param columns 相关列信息列表（按表分组）
     * @return 格式化的提示内容
     */
    String generateNL2SQLPrompt(String question, List<TableDTO> tables, Map<String, List<ColumnDTO>> columns);

    /**
     * 生成自然语言转SQL的提示（包含表关系信息）
     *
     * @param question 用户问题
     * @param tables 相关表信息列表
     * @param columns 相关列信息列表（按表分组）
     * @param tableRelationships 表关系信息
     * @return 格式化的提示内容
     */
    String generateNL2SQLPrompt(String question, List<TableDTO> tables, 
                               Map<String, List<ColumnDTO>> columns, 
                               Map<String, List<String>> tableRelationships);
}