package com.datascope.app.service.llm;

import java.util.Map;

/**
 * LLM集成服务接口
 * 负责与OpenRouter LLM服务集成，处理API调用和响应
 */
public interface LLMIntegrationService {

    /**
     * 发送提示到LLM并获取响应
     *
     * @param prompt 提示内容
     * @return LLM响应文本
     */
    String sendPrompt(String prompt);

    /**
     * 发送提示到LLM并获取响应，支持自定义参数
     *
     * @param prompt 提示内容
     * @param parameters 自定义参数，如temperature、max_tokens等
     * @return LLM响应文本
     */
    String sendPrompt(String prompt, Map<String, Object> parameters);

    /**
     * 获取当前配置的LLM模型名称
     *
     * @return 模型名称
     */
    String getModelName();

    /**
     * 获取当前配置的最大令牌数
     *
     * @return 最大令牌数
     */
    int getMaxTokens();
}