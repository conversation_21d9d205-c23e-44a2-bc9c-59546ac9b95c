package com.datascope.app.service.nl2sql.impl;

import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.service.nl2sql.PromptGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支持跨库查询的提示词生成器
 * 能够生成database.table格式的SQL查询
 */
@Slf4j
@Service
public class CrossDatabasePromptGenerator implements PromptGenerator {

    private static final String CROSS_DB_PROMPT_TEMPLATE =
            "你是一个SQL专家，请将以下自然语言问题转换为跨库SQL查询语句。\n" +
            "问题：%s\n\n" +
            "数据库表结构：\n%s\n" +
            "请生成一个有效的跨库`SQL查询`语句，满足以下要求：\n" +
            "1. 使用标准SQL语法\n" +
            "2. 使用完整的数据库名.表名格式（如：payment_db.user_account）\n" +
            "3. 只使用上述提供的表和列\n" +
            "4. 确保查询语句语法正确且能够执行\n" +
            "5. 返回的SQL应该能够回答用户的问题\n" +
            "6. 不要使用未在表结构中定义的表或列\n" +
            "7. 如果问题中提到了特定的限制条件，请在SQL中体现\n" +
            "8. 支持跨库JOIN操作\n" +
            "9. 如果有多种方案，仅仅需要给出最精确靠谱的一个查询语句\n\n" +
            "请按以下格式返回：\n" +
            "```sql\n" +
            "你的跨库SQL查询语句\n" +
            "```\n\n" +
            "解释：\n" +
            "简要解释你的跨库SQL查询如何解决问题";

    private static final String CROSS_DB_PROMPT_TEMPLATE_WITH_RELATIONSHIPS =
            "你是一个SQL专家，请将以下自然语言问题转换为跨库SQL查询语句。\n" +
            "问题：%s\n\n" +
            "数据库表结构：\n%s\n" +
            "表关系：\n%s\n" +
            "请生成一个有效的跨库SQL查询语句，满足以下要求：\n" +
            "1. 使用标准SQL语法\n" +
            "2. 使用完整的数据库名.表名格式（如：payment_db.user_account）\n" +
            "3. 只使用上述提供的表和列\n" +
            "4. 确保查询语句语法正确且能够执行\n" +
            "5. 返回的SQL应该能够回答用户的问题\n" +
            "6. 不要使用未在表结构中定义的表或列\n" +
            "7. 如果问题中提到了特定的限制条件，请在SQL中体现\n" +
            "8. 使用提供的表关系信息来正确连接跨库表\n" +
            "9. 支持跨库JOIN操作\n\n" +
            "请按以下格式返回：\n" +
            "```sql\n" +
            "你的跨库SQL查询语句\n" +
            "```\n\n" +
            "解释：\n" +
            "简要解释你的跨库SQL查询如何解决问题";

    @Override
    public String generateNL2SQLPrompt(String question, List<TableDTO> tables, Map<String, List<ColumnDTO>> columns) {
        String tableStructure = formatCrossDatabaseTableStructure(tables, columns, null);
        return String.format(CROSS_DB_PROMPT_TEMPLATE, question, tableStructure);
    }

    @Override
    public String generateNL2SQLPrompt(String question, List<TableDTO> tables,
                                      Map<String, List<ColumnDTO>> columns,
                                      Map<String, List<String>> tableRelationships) {
        String tableStructure = formatCrossDatabaseTableStructure(tables, columns, null);
        String relationships = formatTableRelationships(tableRelationships);
        return String.format(CROSS_DB_PROMPT_TEMPLATE_WITH_RELATIONSHIPS, question, tableStructure, relationships);
    }

    /**
     * 生成跨库查询提示词（带schema信息）
     */
    public String generateNL2SQLPromptWithSchema(String question, List<TableDTO> tables,
                                                Map<String, List<ColumnDTO>> columns,
                                                Map<String, String> tableSchemaMap) {
        String tableStructure = formatCrossDatabaseTableStructure(tables, columns, tableSchemaMap);
        return String.format(CROSS_DB_PROMPT_TEMPLATE, question, tableStructure);
    }

    /**
     * 格式化跨库表结构信息
     */
    private String formatCrossDatabaseTableStructure(List<TableDTO> tables, Map<String, List<ColumnDTO>> columns, Map<String, String> tableSchemaMap) {
        StringBuilder sb = new StringBuilder();

        // 按数据库分组显示表信息
        Map<String, List<TableDTO>> tablesByDatabase = groupTablesByDatabase(tables, tableSchemaMap);

        for (Map.Entry<String, List<TableDTO>> entry : tablesByDatabase.entrySet()) {
            String databaseName = entry.getKey();
            List<TableDTO> databaseTables = entry.getValue();

            sb.append("数据库：").append(databaseName).append("\n");
            sb.append(String.join("", Collections.nCopies(databaseName.length() + 4, "="))).append("\n");

            for (TableDTO table : databaseTables) {
                sb.append("表名: ").append(databaseName).append(".").append(table.getName());
                if (table.getDescription() != null && !table.getDescription().isEmpty()) {
                    sb.append(" (描述: ").append(table.getDescription()).append(")");
                }
                sb.append("\n");

                List<ColumnDTO> tableColumns = columns.get(table.getId());
                if (tableColumns != null && !tableColumns.isEmpty()) {
                    sb.append("列: \n");
                    for (ColumnDTO column : tableColumns) {
                        sb.append("  - ").append(column.getName())
                          .append(" (").append(column.getDataType()).append(")")
                          .append(column.getIsPrimaryKey() != null && column.getIsPrimaryKey() ? " [主键]" : "")
                          .append(column.getIsNullable() != null && !column.getIsNullable() ? " [非空]" : "");

                        if (column.getDescription() != null && !column.getDescription().isEmpty()) {
                            sb.append(" 描述: ").append(column.getDescription());
                        }
                        sb.append("\n");
                    }
                } else {
                    sb.append("列信息不可用\n");
                }
                sb.append("\n");
            }
            sb.append("\n");
        }

        return sb.toString();
    }

    /**
     * 按数据库分组表
     */
    private Map<String, List<TableDTO>> groupTablesByDatabase(List<TableDTO> tables, Map<String, String> tableSchemaMap) {
        if (tableSchemaMap != null && !tableSchemaMap.isEmpty()) {
            // 使用提供的schema映射
            return tables.stream()
                    .collect(Collectors.groupingBy(table -> {
                        String schemaName = tableSchemaMap.get(table.getId());
                        if (schemaName != null) {
                            return schemaName;
                        } else {
                            // 兜底方案：使用表名推断
                            return getDatabaseName(table);
                        }
                    }));
        } else {
            // 使用表名推断数据库名
            return tables.stream()
                    .collect(Collectors.groupingBy(table -> getDatabaseName(table)));
        }
    }

    /**
     * 获取表所属的数据库名
     * 通过表名推断数据库名，支持跨库查询
     */
    private String getDatabaseName(TableDTO table) {
        String tableName = table.getName().toLowerCase();

        // 支付相关表
        if (tableName.contains("payment") || tableName.contains("pay") ||
            tableName.contains("transaction") || tableName.contains("txn")) {
            return "payment_db";
        }

        // 订单相关表
        if (tableName.contains("order") || tableName.contains("ord") ||
            tableName.contains("purchase") || tableName.contains("buy")) {
            return "order_db";
        }

        // 用户相关表
        if (tableName.contains("user") || tableName.contains("usr") ||
            tableName.contains("customer") || tableName.contains("member")) {
            return "user_db";
        }

        // 库存相关表
        if (tableName.contains("inventory") || tableName.contains("inv") ||
            tableName.contains("stock") || tableName.contains("warehouse")) {
            return "inventory_db";
        }

        // 商品相关表
        if (tableName.contains("product") || tableName.contains("item") ||
            tableName.contains("goods") || tableName.contains("merchandise")) {
            return "product_db";
        }

        // 财务相关表
        if (tableName.contains("finance") || tableName.contains("account") ||
            tableName.contains("money") || tableName.contains("cash")) {
            return "finance_db";
        }

        // 物流相关表
        if (tableName.contains("logistics") || tableName.contains("shipping") ||
            tableName.contains("delivery") || tableName.contains("transport")) {
            return "logistics_db";
        }

        // 默认数据库
        return "default_db";
    }

    /**
     * 格式化表关系信息
     */
    private String formatTableRelationships(Map<String, List<String>> tableRelationships) {
        if (tableRelationships == null || tableRelationships.isEmpty()) {
            return "无表关系信息";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<String>> entry : tableRelationships.entrySet()) {
            String tableName = entry.getKey();
            List<String> relations = entry.getValue();

            sb.append(tableName).append(" 关联: ");
            sb.append(String.join(", ", relations)).append("\n");
        }

        return sb.toString();
    }
}
