package com.datascope.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.common.response.Response;
import com.datascope.app.config.anno.RepeatCommit;
import com.datascope.app.dto.query.*;
import com.datascope.app.service.QueryService;
import com.datascope.app.vo.response.PageResultVo;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresAuthorization;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresGuest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/queries")
@Tag(name = "查询管理模块", description = "查询管理相关接口")
public class QueryController {

    private final QueryService queryService;

    /**
     * 获取查询列表
     */
    @GetMapping
    @Operation(summary = "获取查询列表")
    public Response<PageResultVo<QueryDTO>> getQueries(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String search,
        @RequestParam(required = false) String searchTerm,
        @RequestParam(required = false) String queryType,
        @RequestParam(required = false) String status,
        @RequestParam(required = false) String serviceStatus,
        @RequestParam(required = false) String dataSourceId,
        @RequestParam(required = false) String sortBy,
        @RequestParam(defaultValue = "desc") String sortDir,
        @RequestParam(defaultValue = "true") boolean includeDrafts) {

        log.info("获取查询列表, page={}, size={}, search={}, searchTerm={}, queryType={}, status={}, serviceStatus={}, dataSourceId={}, sortBy={}, sortDir={}, includeDrafts={}",
            page, size, search, searchTerm, queryType, status, serviceStatus, dataSourceId, sortBy, sortDir, includeDrafts);

        // 优先使用search参数，如果为空则使用searchTerm参数
        String searchKeyword = search;
        if (searchKeyword == null || searchKeyword.trim().isEmpty()) {
            searchKeyword = searchTerm;
        }

        Page<QueryDTO> pageResult = queryService.getQueries(page, size, searchKeyword, queryType, status,
            serviceStatus, dataSourceId, sortBy, sortDir, includeDrafts);

        PageResultVo<QueryDTO> build = PageResultVo.<QueryDTO>builder()
            .items(pageResult.getRecords())
            .total(pageResult.getTotal())
            .page(page).size(size)
            .totalPages(pageResult.getPages())
            .build();
        return Response.ok(build);
    }

    /**
     * 创建新查询
     */
    @PostMapping
    @Operation(summary = "创建新查询")
    public Response<QueryDTO> createQuery(@RequestBody SaveQueryParams params) {
        log.info("创建查询: {}", params);
        return Response.ok(queryService.createQuery(params));
    }

    /**
     * 获取查询详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取查询详情")
    public Response<QueryDTO> getQuery(@PathVariable("id") String id) {
        log.info("获取查询详情: {}", id);
        return Response.ok(queryService.getQuery(id));
    }

    /**
     * 更新查询信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新查询信息")
    public Response<QueryDTO> updateQuery(@PathVariable("id") String id, @RequestBody SaveQueryParams params) {
        log.info("更新查询[{}]: {}", id, params);
        return Response.ok(queryService.updateQuery(id, params));
    }

    /**
     * 删除查询
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除查询")
    public Response<String> deleteQuery(@PathVariable("id") String id) {
        log.info("删除查询: {}", id);
        queryService.deleteQuery(id);
        return Response.ok("查询已成功删除");
    }

    /**
     * 执行查询
     */
    @PostMapping("/{id}/execute")
    @Operation(summary = "执行查询")
    public Response<QueryResultDTO> executeQuery(@PathVariable("id") String id, @RequestBody(required = false) ExecuteQueryParams params) {
        log.info("执行查询[{}]: {}", id, params);

        if (params == null) {
            params = new ExecuteQueryParams();
        }

        return Response.ok(queryService.executeQuery(id, params));
    }

    /**
     * 直接执行SQL（无需保存查询）
     */
    @PostMapping("/execute-sql")
    @Operation(summary = "直接执行SQL（无需保存查询）")
    @RepeatCommit(timeout = 2)
    public Response<QueryResultDTO> executeSQL(@RequestBody ExecuteQueryParams params) {
        log.info("直接执行SQL，数据源[{}]: {}", params.getDataSourceId(), params);
        return Response.ok(queryService.executeSQL(params.getDataSourceId(), params));
    }

    /**
     * 获取收藏的查询列表
     */
    @GetMapping("/favorites")
    @Operation(summary = "获取收藏的查询列表")
    public Response<PageResultVo<QueryDTO>> getFavorites(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String status,
        @RequestParam(required = false) String dataSourceId,
        @RequestParam(required = false) String search) {

        log.info("获取收藏的查询列表, page={}, size={}, status={}, dataSourceId={}, search={}",
            page, size, status, dataSourceId, search);

        Page<QueryDTO> pageResult = queryService.getFavorites(page, size, status, dataSourceId, search);

        PageResultVo<QueryDTO> build = PageResultVo.<QueryDTO>builder()
            .items(pageResult.getRecords())
            .total(pageResult.getTotal())
            .page(page).size(size)
            .totalPages(pageResult.getPages())
            .build();

        return Response.ok(build);
    }

    /**
     * 收藏查询
     */
    @PostMapping("/{id}/favorite")
    @Operation(summary = "收藏查询")
    public Response<String> favoriteQuery(@PathVariable("id") String id) {
        log.info("收藏查询: {}", id);
        queryService.favoriteQuery(id);
        return Response.ok("查询已添加到收藏");
    }

    /**
     * 取消收藏查询
     */
    @Operation(summary = "取消收藏查询")
    @DeleteMapping("/{id}/favorite")
    public Response<String> unfavoriteQuery(@PathVariable("id") String id) {
        log.info("取消收藏查询: {}", id);
        queryService.unfavoriteQuery(id);
        return Response.ok("查询已从收藏中移除");
    }

    /**
     * 获取查询执行历史
     */
    @GetMapping("/{id}/history")
    @Operation(summary = "获取查询执行历史")
    public Response<PageResultVo<ExecutionHistoryDTO>> getExecutionHistory(
        @PathVariable("id") String id,
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size) {

        log.info("获取查询[{}]执行历史, page={}, size={}", id, page, size);

        Page<ExecutionHistoryDTO> pageResult = queryService.getExecutionHistory(id, page, size);

        PageResultVo<ExecutionHistoryDTO> build = PageResultVo.<ExecutionHistoryDTO>builder()
            .items(pageResult.getRecords())
            .total(pageResult.getTotal())
            .page(page).size(size)
            .totalPages(pageResult.getPages())
            .build();

        return Response.ok(build);
    }

    /**
     * 获取查询参数、字段等信息
     */
    @GetMapping("/{id}/parameters")
    @Operation(summary = "获取查询参数、字段等信息")
    public Response<QueryDefinitionDTO> getQueryParameters(@PathVariable("id") String id) {
        log.info("获取查询[{}]参数", id);
        return Response.ok(queryService.getQueryParameters(id));
    }

    /**
     * 分析SQL获取查询参数、字段等信息
     */
    @PostMapping("/analyze-parameters")
    @Operation(summary = "分析SQL获取查询参数、字段等信息")
    public Response<QueryDefinitionDTO> analyzeQueryParameters(@RequestBody AnalyzeQueryParams params) {
        log.info("分析SQL获取参数: {}", params);
        return Response.ok(queryService.analyzeQueryParameters(params));
    }

    /**
     * 获取查询执行计划
     */
    @GetMapping("/{id}/execution-plan")
    @Operation(summary = "获取查询执行计划")
    public Response<ExecutionPlanDTO> getExecutionPlan(@PathVariable("id") String id) {
        log.info("获取查询[{}]执行计划", id);
        return Response.ok(queryService.getExecutionPlan(id));
    }

    /**
     * 获取SQL执行计划（无需保存查询）
     */
    @PostMapping("/execution-plan")
    @Operation(summary = "获取SQL执行计划（无需保存查询）")
    public Response<ExecutionPlanDTO> getExecutionPlanForSQL(@RequestBody ExecutionPlanParams params) {
        log.info("获取SQL执行计划，数据源ID: {}", params.getDataSourceId());
        return Response.ok(queryService.getExecutionPlanForSQL(params));
    }

    /**
     * 获取查询版本列表
     */
    @GetMapping("/{id}/versions")
    @Operation(summary = "获取查询版本列表")
    public Response<PageResultVo<QueryVersionDTO>> getQueryVersions(
        @PathVariable("id") String id,
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String status) {

        log.info("获取查询[{}]版本列表, page={}, size={}, status={}", id, page, size, status);

        Page<QueryVersionDTO> pageResult = queryService.getQueryVersions(id, page, size, status);

        PageResultVo<QueryVersionDTO> build = PageResultVo.<QueryVersionDTO>builder()
            .items(pageResult.getRecords())
            .total(pageResult.getTotal())
            .page(page).size(size)
            .totalPages(pageResult.getPages())
            .build();

        return Response.ok(build);
    }

    /**
     * 创建查询版本
     */
    @PostMapping("/{id}/versions")
    @Operation(summary = "创建查询版本")
    public Response<QueryVersionDTO> createQueryVersion(
        @PathVariable("id") String id,
        @RequestBody Map<String, String> params) {

        log.info("创建查询[{}]版本: {}", id, params);

        String sqlContent = params.get("sqlContent");
        String description = params.get("description");
        String comment = params.get("comment");

        return Response.ok(queryService.createQueryVersion(id, sqlContent, description, comment));
    }

    /**
     * 获取查询版本详情
     */
    @GetMapping("/versions/{versionId}")
    @Operation(summary = "获取查询版本详情")
    public Response<QueryVersionDTO> getQueryVersion(@PathVariable("versionId") String versionId) {
        log.info("获取查询版本详情: {}", versionId);
        return Response.ok(queryService.getQueryVersion(versionId));
    }

    /**
     * 更新查询版本
     */
    @PutMapping("/versions/{versionId}")
    public Response<QueryVersionDTO> updateQueryVersion(
        @PathVariable("versionId") String versionId,
        @RequestBody Map<String, String> params) {

        log.info("更新查询版本[{}]: {}", versionId, params);

        String sqlContent = params.get("sqlContent");
        String description = params.get("description");
        String comment = params.get("comment");

        return Response.ok(queryService.updateQueryVersion(versionId, sqlContent, description, comment));
    }

    /**
     * 发布查询版本
     */
    @Operation(summary = "发布查询版本")
    @PostMapping("/versions/{versionId}/publish")
    public Response<QueryVersionDTO> publishQueryVersion(@PathVariable("versionId") String versionId) {
        log.info("发布查询版本: {}", versionId);
        return Response.ok(queryService.publishQueryVersion(versionId));
    }

    /**
     * 废弃查询版本
     */
    @Operation(summary = "废弃查询版本")
    @PostMapping("/versions/{versionId}/deprecate")
    public Response<QueryVersionDTO> deprecateQueryVersion(@PathVariable("versionId") String versionId) {
        log.info("废弃查询版本: {}", versionId);
        return Response.ok(queryService.deprecateQueryVersion(versionId));
    }

    /**
     * 激活查询版本
     */
    @Operation(summary = "激活查询版本")
    @PostMapping("/{queryId}/versions/{versionId}/activate")
    public Response<Boolean> activateQueryVersion(
        @PathVariable("queryId") String queryId,
        @PathVariable("versionId") String versionId) {

        log.info("激活查询[{}]版本[{}]", queryId, versionId);
        return Response.ok(queryService.activateQueryVersion(queryId, versionId));
    }

    /**
     * 执行特定版本的查询
     */
    @Operation(summary = "执行特定版本的查询", description = "sort 值比如 id,desc  id  id,asc  这三种")
    @PostMapping("/{queryId}/versions/{versionId}/execute")
    public Response<QueryResultDTO> executeQueryVersion(
        @PathVariable("queryId") String queryId,
        @PathVariable("versionId") String versionId,
        @RequestBody(required = false) Map<String, Object> params,
        @RequestParam(required = false) String sort) {
        if (MapUtils.isEmpty(params)) {
            params = new HashMap<>();
        }
        log.info("执行查询[{}]版本[{}]: {}", queryId, versionId, params);
        ExecuteQueryParams executeQueryParams = new ExecuteQueryParams();
        executeQueryParams.setParameters(params);
        executeQueryParams.setPage(params.containsKey("page") && StringUtils.isNumeric(String.valueOf(params.get("page"))) ? Integer.parseInt(String.valueOf(params.get("page"))) : 1);
        executeQueryParams.setSize(params.containsKey("size") && StringUtils.isNumeric(String.valueOf(params.get("size"))) ? Integer.parseInt(String.valueOf(params.get("size"))) : 10);
        executeQueryParams.setSort(sort);

        return Response.ok(queryService.executeQueryVersion(queryId, versionId, executeQueryParams));
    }


    /**
     * 执行特定版本的查询
     */
    @Operation(summary = "执行特定版本动态拼接查询")
    @PostMapping("/{queryId}/versions/{versionId}/{integrationId}/execute-append")
    @RepeatCommit(timeout = 1)
    @RequiresAuthorization(required = false)
    public Response<QueryResultDTO> executeAppendQueryVersion(
        @PathVariable("queryId") String queryId,
        @PathVariable("versionId") String versionId,
        @PathVariable("integrationId") String integrationId,
        @RequestBody(required = false) Map<String, Object> params,
        @RequestParam(required = false) String sort) {
        if (MapUtils.isEmpty(params)) {
            params = new HashMap<>();
        }
        log.info("executeAppendQueryVersion: [{}], version: [{}]: {}", queryId, versionId, params);
        ExecuteQueryParams executeQueryParams = new ExecuteQueryParams();
        executeQueryParams.setParameters(params);
        executeQueryParams.setPage(params.containsKey("page") && StringUtils.isNumeric(String.valueOf(params.get("page"))) ? Integer.parseInt(String.valueOf(params.get("page"))) : 1);
        executeQueryParams.setSize(params.containsKey("size") && StringUtils.isNumeric(String.valueOf(params.get("size"))) ? Integer.parseInt(String.valueOf(params.get("size"))) : 20);
        executeQueryParams.setSort(sort);

        return Response.ok(queryService.executeAppendQuery(queryId, versionId, integrationId, executeQueryParams));
    }

    /**
     * 启用查询
     */
    @Operation(summary = "启用查询")
    @PostMapping("/{id}/enable")
    public Response<Boolean> enableQuery(@PathVariable("id") String id) {
        log.info("启用查询: {}", id);
        return Response.ok(queryService.enableQuery(id));
    }

    /**
     * 禁用查询
     */
    @Operation(summary = "禁用查询")
    @PostMapping("/{id}/disable")
    public Response<Boolean> disableQuery(@PathVariable("id") String id) {
        log.info("禁用查询: {}", id);
        return Response.ok(queryService.disableQuery(id));
    }
}
