package com.datascope.app.controller;

import com.datascope.app.common.response.Response;
import com.datascope.app.dto.query.NaturalLanguageQueryParams;
import com.datascope.app.service.QueryService;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresGuest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 自然语言转SQL
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/natural-language")
@Tag(name = "自然语言", description = "自然语言相关接口")
public class NaturalLanguageController {

    @Autowired
    private QueryService queryService;


    /**
     * 自然语言转SQL
     */
    @PostMapping("/nl-to-sql")
    @Operation(summary = "自然语言转SQL")
    @RequiresGuest
    public Response<QueryService.NaturalLanguageToSqlResult> nlToSql(@RequestBody NaturalLanguageQueryParams params) {
        return Response.ok(queryService.nlToSql(params));
    }
}
