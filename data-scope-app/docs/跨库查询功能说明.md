# 跨库查询功能说明

## 概述

在现有自然语言转SQL功能基础上，直接支持跨库查询。系统使用专门的跨库提示词生成器，生成`database.table`格式的SQL语句。

## 功能特点

1. **直接支持**：所有查询都使用跨库查询方式
2. **智能提示**：使用专门的跨库提示词生成器
3. **格式支持**：生成`database.table`格式的SQL
4. **Schema映射**：利用实际的schema信息进行表分组

## Schema映射机制

### 1. 实际Schema信息
系统利用从数据库获取的实际schema信息进行表分组：
- 在`convertToSQL`方法中，查询schema和table关系时直接建立映射
- 通过`metadataService.getTables(schema.getId())`获取schema下的所有表
- 在查询过程中直接建立`tableSchemaMap`：`tableSchemaMap.put(table.getId(), schema.getName())`
- 避免重复查询，提高效率

### 2. 映射逻辑
- 在查询schema下的表时，直接将表ID映射到schema名称
- 确保每个表都有正确的schema归属信息
- 生成的SQL使用`database.table`格式

## 使用示例

### 1. 跨库查询示例

**问题**：查询用户的订单和支付信息

**Schema映射过程**：
```
DEBUG - 表 user_info 映射到schema: user_db
DEBUG - 表 order_info 映射到schema: order_db  
DEBUG - 表 payment_transaction 映射到schema: payment_db
```

**生成的SQL**：
```sql
SELECT 
    u.user_id,
    u.username,
    o.order_id,
    o.order_amount,
    p.payment_status
FROM user_db.user_info u
LEFT JOIN order_db.order_info o ON u.user_id = o.user_id
LEFT JOIN payment_db.payment_transaction p ON o.order_id = p.order_id
WHERE u.status = 'active';
```

### 2. 单库查询示例

**问题**：查询订单表中的所有订单

**Schema映射过程**：
```
DEBUG - 表 order_info 映射到schema: order_db
```

**生成的SQL**：
```sql
SELECT * FROM order_db.order_info WHERE status = 'active';
```

## 配置说明

### 1. 启用/禁用跨库查询

在`application.yml`中配置：

```yaml
datascope:
  cross-database:
    enabled: true  # 启用跨库查询
```

### 2. 自定义数据库别名

```yaml
datascope:
  cross-database:
    database-aliases:
      payment: payment_db
      order: order_db
      user: user_db
      inventory: inventory_db
```

### 3. 自定义表关联关系

```yaml
datascope:
  cross-database:
    table-relationships:
      user_info:
        - payment_db.user_account
        - order_db.user_profile
      order_info:
        - payment_db.transaction
        - inventory_db.product
```

## API使用

### 1. 自然语言转SQL接口

```bash
POST /api/natural-language/nl-to-sql
Content-Type: application/json

{
    "dataSourceId": "your-datasource-id",
    "question": "查询用户的订单和支付信息"
}
```

### 2. 响应示例

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "sql": "SELECT u.user_id, u.username, o.order_id, o.order_amount, p.payment_status FROM user_db.user_info u LEFT JOIN order_db.order_info o ON u.user_id = o.user_id LEFT JOIN payment_db.payment_transaction p ON o.order_id = p.order_id WHERE u.status = 'active'",
        "explanation": "通过跨库JOIN查询用户的订单和支付信息",
        "tables": ["user_info", "order_info", "payment_transaction"]
    }
}
```

## 日志输出

### 1. 跨库查询日志

```
INFO  - 使用跨库查询提示词生成器
DEBUG - 生成的提示: 你是一个SQL专家，请将以下自然语言问题转换为跨库SQL查询语句...
```

### 2. Schema映射日志

```
DEBUG - 构建表到schema的映射: {table_id_1=payment_db, table_id_2=order_db}
```

## 注意事项

1. **权限要求**：确保数据库用户有跨库查询权限
2. **性能考虑**：跨库JOIN可能影响查询性能
3. **网络延迟**：跨库查询可能增加网络延迟
4. **事务处理**：跨库操作的事务处理需要特别注意

## 扩展配置

### 1. 添加新的数据库别名

```java
// 在CrossDatabaseConfig中添加
databaseAliases.put("finance", "finance_db");
databaseAliases.put("logistics", "logistics_db");
```

### 2. 添加新的表关联关系

```java
// 在CrossDatabaseConfig中添加
tableRelationships.put("finance_record", new String[]{"finance_db.account", "user_db.user_profile"});
```

## 故障排除

### 1. 跨库查询未生效

- 检查`datascope.cross-database.enabled`配置
- 确认问题中包含跨库关键词
- 检查表名是否包含数据库标识

### 2. SQL生成错误

- 检查数据库别名配置
- 确认表关联关系配置正确
- 查看日志中的详细错误信息

### 3. 权限问题

- 确认数据库用户有跨库查询权限
- 检查防火墙和网络连接
- 验证数据库连接配置 