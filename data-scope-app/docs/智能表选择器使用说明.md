# 智能表选择器使用说明

## 概述

智能表选择器是为了解决自然语言转SQL时token消耗过大的问题而设计的。它能够在调用大模型之前，根据用户的自然语言问题智能选择相关的数据库表，从而显著减少发送给大模型的schema信息量。

## 核心优势

1. **大幅减少Token消耗**: 从发送所有表减少到只发送3-6个相关表，通常能节省80-95%的token
2. **提高响应速度**: 减少了大模型需要处理的信息量，提升生成SQL的速度
3. **保持准确性**: 通过智能匹配算法，确保选择的表能够满足用户查询需求
4. **可扩展性**: 支持自定义关键词库和匹配策略

## 工作原理

### 1. 关键词提取
从用户问题中提取关键词，包括：
- 业务关键词（学生、订单、用户等）
- 英文对应词（student、order、user等）
- 问题中的其他有意义词汇

### 2. 相关性评分
为每个表计算相关性得分：
- **表名匹配**: 权重最高（10分）
- **表描述匹配**: 中等权重（5分）
- **字段名匹配**: 较高权重（3分）
- **字段描述匹配**: 较低权重（1分）

### 3. 智能选择
- 选择得分最高的前6个表
- 添加可能的关联表
- 计算整体置信度
- 生成选择原因说明

## 使用方式

### 自动使用（推荐）
当调用`/nl-to-sql`接口时，如果没有指定`contextTables`参数，系统会自动使用智能表选择器：

```json
{
    "dataSourceId": "your-datasource-id",
    "question": "查询学生成绩靠前的top3"
}
```

### 手动指定表（兜底方案）
如果智能选择的结果不满意，可以手动指定相关表：

```json
{
    "dataSourceId": "your-datasource-id", 
    "question": "查询学生成绩靠前的top3",
    "contextTables": ["student_info", "student_score"]
}
```

## 测试用例

### 1. 学生成绩查询
**问题**: "查询学生成绩靠前的top3"
**预期选择**: student_info, student_score
**置信度**: 高（>0.8）

### 2. 订单统计
**问题**: "统计每个用户的订单数量"
**预期选择**: user_info, order_info
**置信度**: 高（>0.7）

### 3. 销售分析
**问题**: "查找上个月销售额最高的产品"
**预期选择**: order_info, product_info, order_item
**置信度**: 中等（0.5-0.7）

## 置信度说明

- **高置信度（>0.7）**: 预期能生成准确的SQL
- **中等置信度（0.5-0.7）**: 结果可能准确，如不满意可手动指定表
- **低置信度（<0.5）**: 建议检查选择结果或手动指定相关表

## 日志输出示例

```
INFO  - 未指定上下文表，使用智能表选择器
INFO  - 数据源共有23个表
INFO  - 智能选择结果: 从23个表中选择了3个表，置信度: 0.85
INFO  - 选择原因: 基于关键词匹配选择了3个表 (置信度: 85.0%):
1. student_info (表名包含关键词: student)
2. student_score (表名包含关键词: student, score; 字段匹配: score)
3. class_info (字段匹配: student_id)

✅ 置信度较高，预期能生成准确的SQL
INFO  - 最终使用3个表生成SQL
```

## 自定义扩展

### 1. 扩展业务关键词
可以在`KeywordBasedTableSelector`中添加特定业务领域的关键词：

```java
// 添加特定业务关键词
BUSINESS_KEYWORDS.put("库存", Arrays.asList("inventory", "stock", "warehouse"));
BUSINESS_KEYWORDS.put("物流", Arrays.asList("logistics", "shipping", "delivery"));
```

### 2. 配置表关联关系
定义表之间的关联关系，当选择某个表时自动包含相关表：

```java
TABLE_RELATIONSHIPS.put("inventory", Arrays.asList("product", "warehouse"));
```

### 3. 实现自定义选择器
可以实现`TableSelector`接口来创建自定义的表选择策略：

```java
@Service
public class CustomTableSelector implements TableSelector {
    @Override
    public TableSelectionResult selectRelevantTables(String question, 
            List<TableDTO> allTables, Map<String, List<ColumnDTO>> allColumns) {
        // 自定义选择逻辑
    }
}
```

## 性能对比

| 场景 | 原始方案 | 智能选择器 | Token节省 |
|------|----------|------------|-----------|
| 小型数据库（10个表） | 100% | 30% | 70% |
| 中型数据库（50个表） | 100% | 12% | 88% |
| 大型数据库（200个表） | 100% | 3% | 97% |

## 注意事项

1. **表命名规范**: 表选择器的效果很大程度上依赖于规范的表命名
2. **业务词汇**: 需要根据具体业务领域调整关键词库
3. **置信度监控**: 建议监控置信度分布，持续优化选择策略
4. **兜底机制**: 始终保留手动指定`contextTables`的能力

## 后续优化方向

1. **语义匹配**: 集成embedding技术进行语义相似度匹配
2. **学习优化**: 根据用户反馈和SQL执行结果优化选择策略
3. **缓存机制**: 缓存常见问题模式的选择结果
4. **多语言支持**: 扩展对其他语言的支持
